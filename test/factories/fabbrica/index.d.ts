import type { Association } from "@prisma/client";
import type { Organization } from "@prisma/client";
import type { Stable } from "@prisma/client";
import type { StableStatus } from "@prisma/client";
import type { User } from "@prisma/client";
import type { UserRole } from "@prisma/client";
import type { Staff } from "@prisma/client";
import type { Contract } from "@prisma/client";
import type { FarmArea } from "@prisma/client";
import type { MasterFarm } from "@prisma/client";
import type { HorseDailyRecord } from "@prisma/client";
import type { HorseBodyPhoto } from "@prisma/client";
import type { HorseBodyAffectedAreaPhoto } from "@prisma/client";
import type { HorseBodyRecord } from "@prisma/client";
import type { HorseTrainingRecord } from "@prisma/client";
import type { TrainingPartner } from "@prisma/client";
import type { OrganizationVeterinarian } from "@prisma/client";
import type { OrganizationFarrier } from "@prisma/client";
import type { HorseMedicalTreatmentRecord } from "@prisma/client";
import type { HorseMedicalTreatmentInvoicePhoto } from "@prisma/client";
import type { HorseMedicalTreatmentAffectedAreaPhoto } from "@prisma/client";
import type { HorseShoeingRecord } from "@prisma/client";
import type { HorseShoeingInvoicePhoto } from "@prisma/client";
import type { HorseRaceResultRecord } from "@prisma/client";
import type { HorseRaceRecapRecord } from "@prisma/client";
import type { RacePlace } from "@prisma/client";
import type { BusinessTripHistory } from "@prisma/client";
import type { BusinessTripHistoryHorse } from "@prisma/client";
import type { Facility } from "@prisma/client";
import type { TrainingCourseMaster } from "@prisma/client";
import type { TrainingCourse } from "@prisma/client";
import type { FurlongLine } from "@prisma/client";
import type { Horse } from "@prisma/client";
import type { HorseStableHistory } from "@prisma/client";
import type { HorseStatus } from "@prisma/client";
import type { MasterHorse } from "@prisma/client";
import type { HorseHandoverNote } from "@prisma/client";
import type { EmailLog } from "@prisma/client";
import type { AiGenerateLog } from "@prisma/client";
import type { OrganizationOwner } from "@prisma/client";
import type { OrganizationOwnerHorseRelation } from "@prisma/client";
import type { Invitation } from "@prisma/client";
import type { Owner } from "@prisma/client";
import type { OwnerHorse } from "@prisma/client";
import type { Report } from "@prisma/client";
import type { ReportSection } from "@prisma/client";
import type { ReportSectionPlainText } from "@prisma/client";
import type { ReportSectionImage } from "@prisma/client";
import type { ReportSectionHorseCondition } from "@prisma/client";
import type { ReportSectionWorkoutCondition } from "@prisma/client";
import type { ReportSectionMonthlySummary } from "@prisma/client";
import type { ReportSectionMonthlySummaryRaceRecord } from "@prisma/client";
import type { ReportSectionMonthlyTimeline } from "@prisma/client";
import type { ReportSectionMonthlyTimelineRecord } from "@prisma/client";
import type { ReportSectionMedicalTreatment } from "@prisma/client";
import type { ReportSectionMedicalTreatmentRecord } from "@prisma/client";
import type { ReportSectionMedicalTreatmentAffectedAreaPhoto } from "@prisma/client";
import type { SentReport } from "@prisma/client";
import type { ShareReport } from "@prisma/client";
import type { PendingSendReport } from "@prisma/client";
import type { ReportGenerateRequest } from "@prisma/client";
import type { StableTmOutsideFarm } from "@prisma/client";
import type { StableTmSection } from "@prisma/client";
import type { StableTmFixedSlot } from "@prisma/client";
import type { StableTmTransportQueueTicket } from "@prisma/client";
import type { StableTmTransportDailyRecord } from "@prisma/client";
import type { StableTmTransportRecord } from "@prisma/client";
import type { StableTmTransportInStatus } from "@prisma/client";
import type { StableTmTransportOutStatus } from "@prisma/client";
import type { StableTmTransportOutHandoverNote } from "@prisma/client";
import type { OnetimeCode } from "@prisma/client";
import type { UserTermsAcceptances } from "@prisma/client";
import type { HorseNoteUserDevice } from "@prisma/client";
import type { FeatureFlag } from "@prisma/client";
import type { BatchJob } from "@prisma/client";
import type { BatchLog } from "@prisma/client";
import type { Training } from "@prisma/client";
import type { TrainingIndicator } from "@prisma/client";
import type { TrainingPeriod } from "@prisma/client";
import type { TrainingIndicatorLabel } from "@prisma/client";
import type { TimeSeriesHeartBeatResult } from "@prisma/client";
import type { GaitAnalysisResult } from "@prisma/client";
import type { HorseCoursePitchAverage } from "@prisma/client";
import type { TrainingMenu } from "@prisma/client";
import type { TrainersUserSettings } from "@prisma/client";
import type { UserLangSetting } from "@prisma/client";
import type { Role } from "@prisma/client";
import type { FurlongLineDirection } from "@prisma/client";
import type { Gender } from "@prisma/client";
import type { InvitationMethod } from "@prisma/client";
import type { BatchStatus } from "@prisma/client";
import type { LogLevel } from "@prisma/client";
import type { PeriodType } from "@prisma/client";
import type { Gait } from "@prisma/client";
import type { Prisma } from "@prisma/client";
import type { Resolver } from "@quramy/prisma-fabbrica/lib/internal";
export { resetSequence, registerScalarFieldValueGenerator, resetScalarFieldValueGenerator } from "@quramy/prisma-fabbrica/lib/internal";
type BuildDataOptions<TTransients extends Record<string, unknown>> = {
    readonly seq: number;
} & TTransients;
type TraitName = string | symbol;
type CallbackDefineOptions<TCreated, TCreateInput, TTransients extends Record<string, unknown>> = {
    onAfterBuild?: (createInput: TCreateInput, transientFields: TTransients) => void | PromiseLike<void>;
    onBeforeCreate?: (createInput: TCreateInput, transientFields: TTransients) => void | PromiseLike<void>;
    onAfterCreate?: (created: TCreated, transientFields: TTransients) => void | PromiseLike<void>;
};
export declare const initialize: (options: import("@quramy/prisma-fabbrica/lib/initialize").InitializeOptions) => void;
type AssociationFactoryDefineInput = {
    associationId?: string;
    associationInternalId?: (bigint | number);
    associationName?: string;
    associationType?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    facilities?: Prisma.FacilityCreateNestedManyWithoutAssociationInput;
    organizations?: Prisma.OrganizationCreateNestedManyWithoutAssociationInput;
};
type AssociationTransientFields = Record<string, unknown> & Partial<Record<keyof AssociationFactoryDefineInput, never>>;
type AssociationFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<AssociationFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Association, Prisma.AssociationCreateInput, TTransients>;
type AssociationFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<AssociationFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: AssociationFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Association, Prisma.AssociationCreateInput, TTransients>;
type AssociationTraitKeys<TOptions extends AssociationFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface AssociationFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Association";
    build(inputData?: Partial<Prisma.AssociationCreateInput & TTransients>): PromiseLike<Prisma.AssociationCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.AssociationCreateInput & TTransients>): PromiseLike<Prisma.AssociationCreateInput>;
    buildList(list: readonly Partial<Prisma.AssociationCreateInput & TTransients>[]): PromiseLike<Prisma.AssociationCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.AssociationCreateInput & TTransients>): PromiseLike<Prisma.AssociationCreateInput[]>;
    pickForConnect(inputData: Association): Pick<Association, "associationId">;
    create(inputData?: Partial<Prisma.AssociationCreateInput & TTransients>): PromiseLike<Association>;
    createList(list: readonly Partial<Prisma.AssociationCreateInput & TTransients>[]): PromiseLike<Association[]>;
    createList(count: number, item?: Partial<Prisma.AssociationCreateInput & TTransients>): PromiseLike<Association[]>;
    createForConnect(inputData?: Partial<Prisma.AssociationCreateInput & TTransients>): PromiseLike<Pick<Association, "associationId">>;
}
export interface AssociationFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends AssociationFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): AssociationFactoryInterfaceWithoutTraits<TTransients>;
}
interface AssociationFactoryBuilder {
    <TOptions extends AssociationFactoryDefineOptions>(options?: TOptions): AssociationFactoryInterface<{}, AssociationTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends AssociationTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends AssociationFactoryDefineOptions<TTransients>>(options?: TOptions) => AssociationFactoryInterface<TTransients, AssociationTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Association} model.
 *
 * @param options
 * @returns factory {@link AssociationFactoryInterface}
 */
export declare const defineAssociationFactory: AssociationFactoryBuilder;
type OrganizationfacilityFactory = {
    _factoryFor: "Facility";
    build: () => PromiseLike<Prisma.FacilityCreateNestedOneWithoutOrganizationsInput["create"]>;
};
type OrganizationassociationFactory = {
    _factoryFor: "Association";
    build: () => PromiseLike<Prisma.AssociationCreateNestedOneWithoutOrganizationsInput["create"]>;
};
type OrganizationfeatureFlagFactory = {
    _factoryFor: "FeatureFlag";
    build: () => PromiseLike<Prisma.FeatureFlagCreateNestedOneWithoutOrganizationInput["create"]>;
};
type OrganizationFactoryDefineInput = {
    organizationUuid?: Buffer;
    organizationId?: number;
    name?: string;
    createdAt?: Date;
    updatedAt?: Date;
    facility?: OrganizationfacilityFactory | Prisma.FacilityCreateNestedOneWithoutOrganizationsInput;
    association?: OrganizationassociationFactory | Prisma.AssociationCreateNestedOneWithoutOrganizationsInput;
    stables?: Prisma.StableCreateNestedManyWithoutOrganizationInput;
    userRoles?: Prisma.UserRoleCreateNestedManyWithoutOrganizationInput;
    horses?: Prisma.HorseCreateNestedManyWithoutOrganizationInput;
    organizationOwners?: Prisma.OrganizationOwnerCreateNestedManyWithoutOrganizationInput;
    reports?: Prisma.ReportCreateNestedManyWithoutOrganizationInput;
    organizationVeterinarians?: Prisma.OrganizationVeterinarianCreateNestedManyWithoutOrganizationInput;
    organizationFarriers?: Prisma.OrganizationFarrierCreateNestedManyWithoutOrganizationInput;
    staffs?: Prisma.StaffCreateNestedManyWithoutOrganizationInput;
    contracts?: Prisma.ContractCreateNestedManyWithoutOrganizationInput;
    featureFlag?: OrganizationfeatureFlagFactory | Prisma.FeatureFlagCreateNestedOneWithoutOrganizationInput;
    horseDailyRecords?: Prisma.HorseDailyRecordCreateNestedManyWithoutOrganizationInput;
    businessTripHistories?: Prisma.BusinessTripHistoryCreateNestedManyWithoutOrganizationInput;
    StableTmOutsideFarm?: Prisma.StableTmOutsideFarmCreateNestedManyWithoutOrganizationInput;
};
type OrganizationTransientFields = Record<string, unknown> & Partial<Record<keyof OrganizationFactoryDefineInput, never>>;
type OrganizationFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OrganizationFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Organization, Prisma.OrganizationCreateInput, TTransients>;
type OrganizationFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<OrganizationFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: OrganizationFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Organization, Prisma.OrganizationCreateInput, TTransients>;
type OrganizationTraitKeys<TOptions extends OrganizationFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OrganizationFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Organization";
    build(inputData?: Partial<Prisma.OrganizationCreateInput & TTransients>): PromiseLike<Prisma.OrganizationCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OrganizationCreateInput & TTransients>): PromiseLike<Prisma.OrganizationCreateInput>;
    buildList(list: readonly Partial<Prisma.OrganizationCreateInput & TTransients>[]): PromiseLike<Prisma.OrganizationCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OrganizationCreateInput & TTransients>): PromiseLike<Prisma.OrganizationCreateInput[]>;
    pickForConnect(inputData: Organization): Pick<Organization, "organizationUuid">;
    create(inputData?: Partial<Prisma.OrganizationCreateInput & TTransients>): PromiseLike<Organization>;
    createList(list: readonly Partial<Prisma.OrganizationCreateInput & TTransients>[]): PromiseLike<Organization[]>;
    createList(count: number, item?: Partial<Prisma.OrganizationCreateInput & TTransients>): PromiseLike<Organization[]>;
    createForConnect(inputData?: Partial<Prisma.OrganizationCreateInput & TTransients>): PromiseLike<Pick<Organization, "organizationUuid">>;
}
export interface OrganizationFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OrganizationFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OrganizationFactoryInterfaceWithoutTraits<TTransients>;
}
interface OrganizationFactoryBuilder {
    <TOptions extends OrganizationFactoryDefineOptions>(options?: TOptions): OrganizationFactoryInterface<{}, OrganizationTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OrganizationTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OrganizationFactoryDefineOptions<TTransients>>(options?: TOptions) => OrganizationFactoryInterface<TTransients, OrganizationTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Organization} model.
 *
 * @param options
 * @returns factory {@link OrganizationFactoryInterface}
 */
export declare const defineOrganizationFactory: OrganizationFactoryBuilder;
type StableorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutStablesInput["create"]>;
};
type StablestableStatusFactory = {
    _factoryFor: "StableStatus";
    build: () => PromiseLike<Prisma.StableStatusCreateNestedOneWithoutStableInput["create"]>;
};
type StableFactoryDefineInput = {
    stableUuid?: Buffer;
    name?: string;
    createdAt?: Date;
    updatedAt?: Date;
    organization: StableorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutStablesInput;
    stableStatus?: StablestableStatusFactory | Prisma.StableStatusCreateNestedOneWithoutStableInput;
    userRoles?: Prisma.UserRoleCreateNestedManyWithoutStableInput;
    horses?: Prisma.HorseCreateNestedManyWithoutStableInput;
    horseStableHistories?: Prisma.HorseStableHistoryCreateNestedManyWithoutStableInput;
    staffs?: Prisma.StaffCreateNestedManyWithoutStableInput;
    trainingMenus?: Prisma.TrainingMenuCreateNestedManyWithoutStableInput;
    trainings?: Prisma.TrainingCreateNestedManyWithoutStableInput;
    stableTmSections?: Prisma.StableTmSectionCreateNestedManyWithoutStableInput;
    stableTmTransportDailyRecord?: Prisma.StableTmTransportDailyRecordCreateNestedManyWithoutStableInput;
};
type StableTransientFields = Record<string, unknown> & Partial<Record<keyof StableFactoryDefineInput, never>>;
type StableFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Stable, Prisma.StableCreateInput, TTransients>;
type StableFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Stable, Prisma.StableCreateInput, TTransients>;
type StableTraitKeys<TOptions extends StableFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Stable";
    build(inputData?: Partial<Prisma.StableCreateInput & TTransients>): PromiseLike<Prisma.StableCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableCreateInput & TTransients>): PromiseLike<Prisma.StableCreateInput>;
    buildList(list: readonly Partial<Prisma.StableCreateInput & TTransients>[]): PromiseLike<Prisma.StableCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableCreateInput & TTransients>): PromiseLike<Prisma.StableCreateInput[]>;
    pickForConnect(inputData: Stable): Pick<Stable, "stableUuid">;
    create(inputData?: Partial<Prisma.StableCreateInput & TTransients>): PromiseLike<Stable>;
    createList(list: readonly Partial<Prisma.StableCreateInput & TTransients>[]): PromiseLike<Stable[]>;
    createList(count: number, item?: Partial<Prisma.StableCreateInput & TTransients>): PromiseLike<Stable[]>;
    createForConnect(inputData?: Partial<Prisma.StableCreateInput & TTransients>): PromiseLike<Pick<Stable, "stableUuid">>;
}
export interface StableFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableFactoryBuilder {
    <TOptions extends StableFactoryDefineOptions>(options: TOptions): StableFactoryInterface<{}, StableTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableFactoryDefineOptions<TTransients>>(options: TOptions) => StableFactoryInterface<TTransients, StableTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Stable} model.
 *
 * @param options
 * @returns factory {@link StableFactoryInterface}
 */
export declare const defineStableFactory: StableFactoryBuilder;
type StableStatusstableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutStableStatusInput["create"]>;
};
type StableStatusFactoryDefineInput = {
    stableStatusId?: (bigint | number);
    stallNum?: number;
    createdAt?: Date;
    updatedAt?: Date;
    stable?: StableStatusstableFactory | Prisma.StableCreateNestedOneWithoutStableStatusInput;
};
type StableStatusTransientFields = Record<string, unknown> & Partial<Record<keyof StableStatusFactoryDefineInput, never>>;
type StableStatusFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableStatusFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableStatus, Prisma.StableStatusCreateInput, TTransients>;
type StableStatusFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<StableStatusFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: StableStatusFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableStatus, Prisma.StableStatusCreateInput, TTransients>;
type StableStatusTraitKeys<TOptions extends StableStatusFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableStatusFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableStatus";
    build(inputData?: Partial<Prisma.StableStatusCreateInput & TTransients>): PromiseLike<Prisma.StableStatusCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableStatusCreateInput & TTransients>): PromiseLike<Prisma.StableStatusCreateInput>;
    buildList(list: readonly Partial<Prisma.StableStatusCreateInput & TTransients>[]): PromiseLike<Prisma.StableStatusCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableStatusCreateInput & TTransients>): PromiseLike<Prisma.StableStatusCreateInput[]>;
    pickForConnect(inputData: StableStatus): Pick<StableStatus, "stableStatusId">;
    create(inputData?: Partial<Prisma.StableStatusCreateInput & TTransients>): PromiseLike<StableStatus>;
    createList(list: readonly Partial<Prisma.StableStatusCreateInput & TTransients>[]): PromiseLike<StableStatus[]>;
    createList(count: number, item?: Partial<Prisma.StableStatusCreateInput & TTransients>): PromiseLike<StableStatus[]>;
    createForConnect(inputData?: Partial<Prisma.StableStatusCreateInput & TTransients>): PromiseLike<Pick<StableStatus, "stableStatusId">>;
}
export interface StableStatusFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableStatusFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableStatusFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableStatusFactoryBuilder {
    <TOptions extends StableStatusFactoryDefineOptions>(options?: TOptions): StableStatusFactoryInterface<{}, StableStatusTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableStatusTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableStatusFactoryDefineOptions<TTransients>>(options?: TOptions) => StableStatusFactoryInterface<TTransients, StableStatusTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableStatus} model.
 *
 * @param options
 * @returns factory {@link StableStatusFactoryInterface}
 */
export declare const defineStableStatusFactory: StableStatusFactoryBuilder;
type UserFactoryDefineInput = {
    userUuid?: Buffer;
    userId?: number;
    firebaseUid?: string;
    firstName?: string;
    middleName?: string | null;
    lastName?: string;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    userRoles?: Prisma.UserRoleCreateNestedManyWithoutUserInput;
    staffs?: Prisma.StaffCreateNestedManyWithoutUserInput;
    HorseNoteUserDevice?: Prisma.HorseNoteUserDeviceCreateNestedManyWithoutUserInput;
    TrainersUserSettings?: Prisma.TrainersUserSettingsCreateNestedManyWithoutUserInput;
    UserLangSettings?: Prisma.UserLangSettingCreateNestedManyWithoutUserInput;
    horseDailyRecords?: Prisma.HorseDailyRecordCreateNestedManyWithoutCreatedUserInput;
};
type UserTransientFields = Record<string, unknown> & Partial<Record<keyof UserFactoryDefineInput, never>>;
type UserFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<UserFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<User, Prisma.UserCreateInput, TTransients>;
type UserFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<UserFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: UserFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<User, Prisma.UserCreateInput, TTransients>;
type UserTraitKeys<TOptions extends UserFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface UserFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "User";
    build(inputData?: Partial<Prisma.UserCreateInput & TTransients>): PromiseLike<Prisma.UserCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.UserCreateInput & TTransients>): PromiseLike<Prisma.UserCreateInput>;
    buildList(list: readonly Partial<Prisma.UserCreateInput & TTransients>[]): PromiseLike<Prisma.UserCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.UserCreateInput & TTransients>): PromiseLike<Prisma.UserCreateInput[]>;
    pickForConnect(inputData: User): Pick<User, "userUuid">;
    create(inputData?: Partial<Prisma.UserCreateInput & TTransients>): PromiseLike<User>;
    createList(list: readonly Partial<Prisma.UserCreateInput & TTransients>[]): PromiseLike<User[]>;
    createList(count: number, item?: Partial<Prisma.UserCreateInput & TTransients>): PromiseLike<User[]>;
    createForConnect(inputData?: Partial<Prisma.UserCreateInput & TTransients>): PromiseLike<Pick<User, "userUuid">>;
}
export interface UserFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends UserFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): UserFactoryInterfaceWithoutTraits<TTransients>;
}
interface UserFactoryBuilder {
    <TOptions extends UserFactoryDefineOptions>(options?: TOptions): UserFactoryInterface<{}, UserTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends UserTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends UserFactoryDefineOptions<TTransients>>(options?: TOptions) => UserFactoryInterface<TTransients, UserTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link User} model.
 *
 * @param options
 * @returns factory {@link UserFactoryInterface}
 */
export declare const defineUserFactory: UserFactoryBuilder;
type UserRoleorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutUserRolesInput["create"]>;
};
type UserRolestableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutUserRolesInput["create"]>;
};
type UserRoleuserFactory = {
    _factoryFor: "User";
    build: () => PromiseLike<Prisma.UserCreateNestedOneWithoutUserRolesInput["create"]>;
};
type UserRoleFactoryDefineInput = {
    roleUuid?: Buffer;
    roleId?: number;
    role?: Role;
    createdAt?: Date;
    updatedAt?: Date;
    organization?: UserRoleorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutUserRolesInput;
    stable?: UserRolestableFactory | Prisma.StableCreateNestedOneWithoutUserRolesInput;
    user?: UserRoleuserFactory | Prisma.UserCreateNestedOneWithoutUserRolesInput;
};
type UserRoleTransientFields = Record<string, unknown> & Partial<Record<keyof UserRoleFactoryDefineInput, never>>;
type UserRoleFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<UserRoleFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<UserRole, Prisma.UserRoleCreateInput, TTransients>;
type UserRoleFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<UserRoleFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: UserRoleFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<UserRole, Prisma.UserRoleCreateInput, TTransients>;
type UserRoleTraitKeys<TOptions extends UserRoleFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface UserRoleFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "UserRole";
    build(inputData?: Partial<Prisma.UserRoleCreateInput & TTransients>): PromiseLike<Prisma.UserRoleCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.UserRoleCreateInput & TTransients>): PromiseLike<Prisma.UserRoleCreateInput>;
    buildList(list: readonly Partial<Prisma.UserRoleCreateInput & TTransients>[]): PromiseLike<Prisma.UserRoleCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.UserRoleCreateInput & TTransients>): PromiseLike<Prisma.UserRoleCreateInput[]>;
    pickForConnect(inputData: UserRole): Pick<UserRole, "roleUuid">;
    create(inputData?: Partial<Prisma.UserRoleCreateInput & TTransients>): PromiseLike<UserRole>;
    createList(list: readonly Partial<Prisma.UserRoleCreateInput & TTransients>[]): PromiseLike<UserRole[]>;
    createList(count: number, item?: Partial<Prisma.UserRoleCreateInput & TTransients>): PromiseLike<UserRole[]>;
    createForConnect(inputData?: Partial<Prisma.UserRoleCreateInput & TTransients>): PromiseLike<Pick<UserRole, "roleUuid">>;
}
export interface UserRoleFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends UserRoleFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): UserRoleFactoryInterfaceWithoutTraits<TTransients>;
}
interface UserRoleFactoryBuilder {
    <TOptions extends UserRoleFactoryDefineOptions>(options?: TOptions): UserRoleFactoryInterface<{}, UserRoleTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends UserRoleTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends UserRoleFactoryDefineOptions<TTransients>>(options?: TOptions) => UserRoleFactoryInterface<TTransients, UserRoleTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link UserRole} model.
 *
 * @param options
 * @returns factory {@link UserRoleFactoryInterface}
 */
export declare const defineUserRoleFactory: UserRoleFactoryBuilder;
type StaffstableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutStaffsInput["create"]>;
};
type StafforganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutStaffsInput["create"]>;
};
type StaffuserFactory = {
    _factoryFor: "User";
    build: () => PromiseLike<Prisma.UserCreateNestedOneWithoutStaffsInput["create"]>;
};
type StaffFactoryDefineInput = {
    staffUuid?: Buffer;
    name?: string;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    stable?: StaffstableFactory | Prisma.StableCreateNestedOneWithoutStaffsInput;
    organization?: StafforganizationFactory | Prisma.OrganizationCreateNestedOneWithoutStaffsInput;
    user?: StaffuserFactory | Prisma.UserCreateNestedOneWithoutStaffsInput;
    horseRaceRecapRecords?: Prisma.HorseRaceRecapRecordCreateNestedManyWithoutStaffInput;
    horseTrainingRecords?: Prisma.HorseTrainingRecordCreateNestedManyWithoutRiderInput;
    StableTmTransportOutStatus?: Prisma.StableTmTransportOutStatusCreateNestedManyWithoutStaffInput;
    StableTmTransportInStatus?: Prisma.StableTmTransportInStatusCreateNestedManyWithoutStaffInput;
};
type StaffTransientFields = Record<string, unknown> & Partial<Record<keyof StaffFactoryDefineInput, never>>;
type StaffFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StaffFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Staff, Prisma.StaffCreateInput, TTransients>;
type StaffFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<StaffFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: StaffFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Staff, Prisma.StaffCreateInput, TTransients>;
type StaffTraitKeys<TOptions extends StaffFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StaffFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Staff";
    build(inputData?: Partial<Prisma.StaffCreateInput & TTransients>): PromiseLike<Prisma.StaffCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StaffCreateInput & TTransients>): PromiseLike<Prisma.StaffCreateInput>;
    buildList(list: readonly Partial<Prisma.StaffCreateInput & TTransients>[]): PromiseLike<Prisma.StaffCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StaffCreateInput & TTransients>): PromiseLike<Prisma.StaffCreateInput[]>;
    pickForConnect(inputData: Staff): Pick<Staff, "staffUuid">;
    create(inputData?: Partial<Prisma.StaffCreateInput & TTransients>): PromiseLike<Staff>;
    createList(list: readonly Partial<Prisma.StaffCreateInput & TTransients>[]): PromiseLike<Staff[]>;
    createList(count: number, item?: Partial<Prisma.StaffCreateInput & TTransients>): PromiseLike<Staff[]>;
    createForConnect(inputData?: Partial<Prisma.StaffCreateInput & TTransients>): PromiseLike<Pick<Staff, "staffUuid">>;
}
export interface StaffFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StaffFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StaffFactoryInterfaceWithoutTraits<TTransients>;
}
interface StaffFactoryBuilder {
    <TOptions extends StaffFactoryDefineOptions>(options?: TOptions): StaffFactoryInterface<{}, StaffTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StaffTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StaffFactoryDefineOptions<TTransients>>(options?: TOptions) => StaffFactoryInterface<TTransients, StaffTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Staff} model.
 *
 * @param options
 * @returns factory {@link StaffFactoryInterface}
 */
export declare const defineStaffFactory: StaffFactoryBuilder;
type ContractorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutContractsInput["create"]>;
};
type ContractFactoryDefineInput = {
    contractId?: (bigint | number);
    hasHnContract?: boolean;
    hasOrmContract?: boolean;
    hasOrmAiContract?: boolean;
    hasStmContracts?: boolean;
    hasRmContract?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    organization: ContractorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutContractsInput;
};
type ContractTransientFields = Record<string, unknown> & Partial<Record<keyof ContractFactoryDefineInput, never>>;
type ContractFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ContractFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Contract, Prisma.ContractCreateInput, TTransients>;
type ContractFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ContractFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ContractFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Contract, Prisma.ContractCreateInput, TTransients>;
type ContractTraitKeys<TOptions extends ContractFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ContractFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Contract";
    build(inputData?: Partial<Prisma.ContractCreateInput & TTransients>): PromiseLike<Prisma.ContractCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ContractCreateInput & TTransients>): PromiseLike<Prisma.ContractCreateInput>;
    buildList(list: readonly Partial<Prisma.ContractCreateInput & TTransients>[]): PromiseLike<Prisma.ContractCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ContractCreateInput & TTransients>): PromiseLike<Prisma.ContractCreateInput[]>;
    pickForConnect(inputData: Contract): Pick<Contract, "contractId">;
    create(inputData?: Partial<Prisma.ContractCreateInput & TTransients>): PromiseLike<Contract>;
    createList(list: readonly Partial<Prisma.ContractCreateInput & TTransients>[]): PromiseLike<Contract[]>;
    createList(count: number, item?: Partial<Prisma.ContractCreateInput & TTransients>): PromiseLike<Contract[]>;
    createForConnect(inputData?: Partial<Prisma.ContractCreateInput & TTransients>): PromiseLike<Pick<Contract, "contractId">>;
}
export interface ContractFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ContractFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ContractFactoryInterfaceWithoutTraits<TTransients>;
}
interface ContractFactoryBuilder {
    <TOptions extends ContractFactoryDefineOptions>(options: TOptions): ContractFactoryInterface<{}, ContractTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ContractTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ContractFactoryDefineOptions<TTransients>>(options: TOptions) => ContractFactoryInterface<TTransients, ContractTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Contract} model.
 *
 * @param options
 * @returns factory {@link ContractFactoryInterface}
 */
export declare const defineContractFactory: ContractFactoryBuilder;
type FarmAreaFactoryDefineInput = {
    farmAreaId?: Buffer;
    farmAreaInternalId?: (bigint | number);
    name?: string;
    order?: number;
    createdAt?: Date;
    updatedAt?: Date;
    masterFarms?: Prisma.MasterFarmCreateNestedManyWithoutFarmAreaInput;
    StableTmOutsideFarm?: Prisma.StableTmOutsideFarmCreateNestedManyWithoutFarmAreaInput;
};
type FarmAreaTransientFields = Record<string, unknown> & Partial<Record<keyof FarmAreaFactoryDefineInput, never>>;
type FarmAreaFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<FarmAreaFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<FarmArea, Prisma.FarmAreaCreateInput, TTransients>;
type FarmAreaFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<FarmAreaFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: FarmAreaFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<FarmArea, Prisma.FarmAreaCreateInput, TTransients>;
type FarmAreaTraitKeys<TOptions extends FarmAreaFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface FarmAreaFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "FarmArea";
    build(inputData?: Partial<Prisma.FarmAreaCreateInput & TTransients>): PromiseLike<Prisma.FarmAreaCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.FarmAreaCreateInput & TTransients>): PromiseLike<Prisma.FarmAreaCreateInput>;
    buildList(list: readonly Partial<Prisma.FarmAreaCreateInput & TTransients>[]): PromiseLike<Prisma.FarmAreaCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.FarmAreaCreateInput & TTransients>): PromiseLike<Prisma.FarmAreaCreateInput[]>;
    pickForConnect(inputData: FarmArea): Pick<FarmArea, "farmAreaId">;
    create(inputData?: Partial<Prisma.FarmAreaCreateInput & TTransients>): PromiseLike<FarmArea>;
    createList(list: readonly Partial<Prisma.FarmAreaCreateInput & TTransients>[]): PromiseLike<FarmArea[]>;
    createList(count: number, item?: Partial<Prisma.FarmAreaCreateInput & TTransients>): PromiseLike<FarmArea[]>;
    createForConnect(inputData?: Partial<Prisma.FarmAreaCreateInput & TTransients>): PromiseLike<Pick<FarmArea, "farmAreaId">>;
}
export interface FarmAreaFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends FarmAreaFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): FarmAreaFactoryInterfaceWithoutTraits<TTransients>;
}
interface FarmAreaFactoryBuilder {
    <TOptions extends FarmAreaFactoryDefineOptions>(options?: TOptions): FarmAreaFactoryInterface<{}, FarmAreaTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends FarmAreaTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends FarmAreaFactoryDefineOptions<TTransients>>(options?: TOptions) => FarmAreaFactoryInterface<TTransients, FarmAreaTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link FarmArea} model.
 *
 * @param options
 * @returns factory {@link FarmAreaFactoryInterface}
 */
export declare const defineFarmAreaFactory: FarmAreaFactoryBuilder;
type MasterFarmfarmAreaFactory = {
    _factoryFor: "FarmArea";
    build: () => PromiseLike<Prisma.FarmAreaCreateNestedOneWithoutMasterFarmsInput["create"]>;
};
type MasterFarmFactoryDefineInput = {
    masterFarmId?: Buffer;
    masterFarmInternalId?: (bigint | number);
    name?: string;
    createdAt?: Date;
    updatedAt?: Date;
    farmArea: MasterFarmfarmAreaFactory | Prisma.FarmAreaCreateNestedOneWithoutMasterFarmsInput;
    stableTmOutsideFarms?: Prisma.StableTmOutsideFarmCreateNestedManyWithoutMasterFarmInput;
};
type MasterFarmTransientFields = Record<string, unknown> & Partial<Record<keyof MasterFarmFactoryDefineInput, never>>;
type MasterFarmFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<MasterFarmFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<MasterFarm, Prisma.MasterFarmCreateInput, TTransients>;
type MasterFarmFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<MasterFarmFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: MasterFarmFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<MasterFarm, Prisma.MasterFarmCreateInput, TTransients>;
type MasterFarmTraitKeys<TOptions extends MasterFarmFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface MasterFarmFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "MasterFarm";
    build(inputData?: Partial<Prisma.MasterFarmCreateInput & TTransients>): PromiseLike<Prisma.MasterFarmCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.MasterFarmCreateInput & TTransients>): PromiseLike<Prisma.MasterFarmCreateInput>;
    buildList(list: readonly Partial<Prisma.MasterFarmCreateInput & TTransients>[]): PromiseLike<Prisma.MasterFarmCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.MasterFarmCreateInput & TTransients>): PromiseLike<Prisma.MasterFarmCreateInput[]>;
    pickForConnect(inputData: MasterFarm): Pick<MasterFarm, "masterFarmId">;
    create(inputData?: Partial<Prisma.MasterFarmCreateInput & TTransients>): PromiseLike<MasterFarm>;
    createList(list: readonly Partial<Prisma.MasterFarmCreateInput & TTransients>[]): PromiseLike<MasterFarm[]>;
    createList(count: number, item?: Partial<Prisma.MasterFarmCreateInput & TTransients>): PromiseLike<MasterFarm[]>;
    createForConnect(inputData?: Partial<Prisma.MasterFarmCreateInput & TTransients>): PromiseLike<Pick<MasterFarm, "masterFarmId">>;
}
export interface MasterFarmFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends MasterFarmFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): MasterFarmFactoryInterfaceWithoutTraits<TTransients>;
}
interface MasterFarmFactoryBuilder {
    <TOptions extends MasterFarmFactoryDefineOptions>(options: TOptions): MasterFarmFactoryInterface<{}, MasterFarmTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends MasterFarmTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends MasterFarmFactoryDefineOptions<TTransients>>(options: TOptions) => MasterFarmFactoryInterface<TTransients, MasterFarmTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link MasterFarm} model.
 *
 * @param options
 * @returns factory {@link MasterFarmFactoryInterface}
 */
export declare const defineMasterFarmFactory: MasterFarmFactoryBuilder;
type HorseDailyRecordorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutHorseDailyRecordsInput["create"]>;
};
type HorseDailyRecordhorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutHorseDailyRecordsInput["create"]>;
};
type HorseDailyRecordcreatedUserFactory = {
    _factoryFor: "User";
    build: () => PromiseLike<Prisma.UserCreateNestedOneWithoutHorseDailyRecordsInput["create"]>;
};
type HorseDailyRecordhorseBodyRecordFactory = {
    _factoryFor: "HorseBodyRecord";
    build: () => PromiseLike<Prisma.HorseBodyRecordCreateNestedOneWithoutHorseDailyRecordInput["create"]>;
};
type HorseDailyRecordhorseRaceResultRecordFactory = {
    _factoryFor: "HorseRaceResultRecord";
    build: () => PromiseLike<Prisma.HorseRaceResultRecordCreateNestedOneWithoutHorseDailyRecordInput["create"]>;
};
type HorseDailyRecordhorseRaceRecapRecordFactory = {
    _factoryFor: "HorseRaceRecapRecord";
    build: () => PromiseLike<Prisma.HorseRaceRecapRecordCreateNestedOneWithoutHorseDailyRecordInput["create"]>;
};
type HorseDailyRecordFactoryDefineInput = {
    horseDailyRecordId?: Buffer;
    year?: number;
    month?: number;
    day?: number;
    createdAt?: Date;
    updatedAt?: Date;
    organization: HorseDailyRecordorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutHorseDailyRecordsInput;
    horse: HorseDailyRecordhorseFactory | Prisma.HorseCreateNestedOneWithoutHorseDailyRecordsInput;
    createdUser?: HorseDailyRecordcreatedUserFactory | Prisma.UserCreateNestedOneWithoutHorseDailyRecordsInput;
    horseBodyPhotos?: Prisma.HorseBodyPhotoCreateNestedManyWithoutHorseDailyRecordInput;
    horseBodyRecord?: HorseDailyRecordhorseBodyRecordFactory | Prisma.HorseBodyRecordCreateNestedOneWithoutHorseDailyRecordInput;
    horseRaceResultRecord?: HorseDailyRecordhorseRaceResultRecordFactory | Prisma.HorseRaceResultRecordCreateNestedOneWithoutHorseDailyRecordInput;
    horseRaceRecapRecord?: HorseDailyRecordhorseRaceRecapRecordFactory | Prisma.HorseRaceRecapRecordCreateNestedOneWithoutHorseDailyRecordInput;
    horseShoeingRecords?: Prisma.HorseShoeingRecordCreateNestedManyWithoutHorseDailyRecordInput;
    horseMedicalTreatmentRecords?: Prisma.HorseMedicalTreatmentRecordCreateNestedManyWithoutHorseDailyRecordInput;
    horseTrainingRecords?: Prisma.HorseTrainingRecordCreateNestedManyWithoutHorseDailyRecordInput;
    horseBodyAffectedAreaPhotos?: Prisma.HorseBodyAffectedAreaPhotoCreateNestedManyWithoutHorseDailyRecordInput;
};
type HorseDailyRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseDailyRecordFactoryDefineInput, never>>;
type HorseDailyRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseDailyRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseDailyRecord, Prisma.HorseDailyRecordCreateInput, TTransients>;
type HorseDailyRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseDailyRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseDailyRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseDailyRecord, Prisma.HorseDailyRecordCreateInput, TTransients>;
type HorseDailyRecordTraitKeys<TOptions extends HorseDailyRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseDailyRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseDailyRecord";
    build(inputData?: Partial<Prisma.HorseDailyRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseDailyRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseDailyRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseDailyRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseDailyRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseDailyRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseDailyRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseDailyRecordCreateInput[]>;
    pickForConnect(inputData: HorseDailyRecord): Pick<HorseDailyRecord, "horseDailyRecordId">;
    create(inputData?: Partial<Prisma.HorseDailyRecordCreateInput & TTransients>): PromiseLike<HorseDailyRecord>;
    createList(list: readonly Partial<Prisma.HorseDailyRecordCreateInput & TTransients>[]): PromiseLike<HorseDailyRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseDailyRecordCreateInput & TTransients>): PromiseLike<HorseDailyRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseDailyRecordCreateInput & TTransients>): PromiseLike<Pick<HorseDailyRecord, "horseDailyRecordId">>;
}
export interface HorseDailyRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseDailyRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseDailyRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseDailyRecordFactoryBuilder {
    <TOptions extends HorseDailyRecordFactoryDefineOptions>(options: TOptions): HorseDailyRecordFactoryInterface<{}, HorseDailyRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseDailyRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseDailyRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseDailyRecordFactoryInterface<TTransients, HorseDailyRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseDailyRecord} model.
 *
 * @param options
 * @returns factory {@link HorseDailyRecordFactoryInterface}
 */
export declare const defineHorseDailyRecordFactory: HorseDailyRecordFactoryBuilder;
type HorseBodyPhotohorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseBodyPhotosInput["create"]>;
};
type HorseBodyPhotoFactoryDefineInput = {
    horseBodyPhotoId?: Buffer;
    photoPath?: string;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseBodyPhotohorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseBodyPhotosInput;
};
type HorseBodyPhotoTransientFields = Record<string, unknown> & Partial<Record<keyof HorseBodyPhotoFactoryDefineInput, never>>;
type HorseBodyPhotoFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseBodyPhotoFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseBodyPhoto, Prisma.HorseBodyPhotoCreateInput, TTransients>;
type HorseBodyPhotoFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseBodyPhotoFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseBodyPhotoFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseBodyPhoto, Prisma.HorseBodyPhotoCreateInput, TTransients>;
type HorseBodyPhotoTraitKeys<TOptions extends HorseBodyPhotoFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseBodyPhotoFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseBodyPhoto";
    build(inputData?: Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyPhotoCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyPhotoCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>[]): PromiseLike<Prisma.HorseBodyPhotoCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyPhotoCreateInput[]>;
    pickForConnect(inputData: HorseBodyPhoto): Pick<HorseBodyPhoto, "horseBodyPhotoId">;
    create(inputData?: Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>): PromiseLike<HorseBodyPhoto>;
    createList(list: readonly Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>[]): PromiseLike<HorseBodyPhoto[]>;
    createList(count: number, item?: Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>): PromiseLike<HorseBodyPhoto[]>;
    createForConnect(inputData?: Partial<Prisma.HorseBodyPhotoCreateInput & TTransients>): PromiseLike<Pick<HorseBodyPhoto, "horseBodyPhotoId">>;
}
export interface HorseBodyPhotoFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseBodyPhotoFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseBodyPhotoFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseBodyPhotoFactoryBuilder {
    <TOptions extends HorseBodyPhotoFactoryDefineOptions>(options: TOptions): HorseBodyPhotoFactoryInterface<{}, HorseBodyPhotoTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseBodyPhotoTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseBodyPhotoFactoryDefineOptions<TTransients>>(options: TOptions) => HorseBodyPhotoFactoryInterface<TTransients, HorseBodyPhotoTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseBodyPhoto} model.
 *
 * @param options
 * @returns factory {@link HorseBodyPhotoFactoryInterface}
 */
export declare const defineHorseBodyPhotoFactory: HorseBodyPhotoFactoryBuilder;
type HorseBodyAffectedAreaPhotohorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseBodyAffectedAreaPhotosInput["create"]>;
};
type HorseBodyAffectedAreaPhotoFactoryDefineInput = {
    horseBodyAffectedAreaPhotoId?: Buffer;
    horseBodyAffectedAreaPhotoInternalId?: (bigint | number);
    daypart?: string;
    photoPath?: string;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseBodyAffectedAreaPhotohorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseBodyAffectedAreaPhotosInput;
};
type HorseBodyAffectedAreaPhotoTransientFields = Record<string, unknown> & Partial<Record<keyof HorseBodyAffectedAreaPhotoFactoryDefineInput, never>>;
type HorseBodyAffectedAreaPhotoFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseBodyAffectedAreaPhotoFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseBodyAffectedAreaPhoto, Prisma.HorseBodyAffectedAreaPhotoCreateInput, TTransients>;
type HorseBodyAffectedAreaPhotoFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseBodyAffectedAreaPhotoFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseBodyAffectedAreaPhotoFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseBodyAffectedAreaPhoto, Prisma.HorseBodyAffectedAreaPhotoCreateInput, TTransients>;
type HorseBodyAffectedAreaPhotoTraitKeys<TOptions extends HorseBodyAffectedAreaPhotoFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseBodyAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseBodyAffectedAreaPhoto";
    build(inputData?: Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyAffectedAreaPhotoCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyAffectedAreaPhotoCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>[]): PromiseLike<Prisma.HorseBodyAffectedAreaPhotoCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyAffectedAreaPhotoCreateInput[]>;
    pickForConnect(inputData: HorseBodyAffectedAreaPhoto): Pick<HorseBodyAffectedAreaPhoto, "horseBodyAffectedAreaPhotoId">;
    create(inputData?: Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<HorseBodyAffectedAreaPhoto>;
    createList(list: readonly Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>[]): PromiseLike<HorseBodyAffectedAreaPhoto[]>;
    createList(count: number, item?: Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<HorseBodyAffectedAreaPhoto[]>;
    createForConnect(inputData?: Partial<Prisma.HorseBodyAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Pick<HorseBodyAffectedAreaPhoto, "horseBodyAffectedAreaPhotoId">>;
}
export interface HorseBodyAffectedAreaPhotoFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseBodyAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseBodyAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseBodyAffectedAreaPhotoFactoryBuilder {
    <TOptions extends HorseBodyAffectedAreaPhotoFactoryDefineOptions>(options: TOptions): HorseBodyAffectedAreaPhotoFactoryInterface<{}, HorseBodyAffectedAreaPhotoTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseBodyAffectedAreaPhotoTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseBodyAffectedAreaPhotoFactoryDefineOptions<TTransients>>(options: TOptions) => HorseBodyAffectedAreaPhotoFactoryInterface<TTransients, HorseBodyAffectedAreaPhotoTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseBodyAffectedAreaPhoto} model.
 *
 * @param options
 * @returns factory {@link HorseBodyAffectedAreaPhotoFactoryInterface}
 */
export declare const defineHorseBodyAffectedAreaPhotoFactory: HorseBodyAffectedAreaPhotoFactoryBuilder;
type HorseBodyRecordhorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseBodyRecordInput["create"]>;
};
type HorseBodyRecordFactoryDefineInput = {
    horseBodyRecordId?: Buffer;
    bodyWeight?: number | null;
    amBodyTemperature?: number | null;
    pmBodyTemperature?: number | null;
    amHorseBodyComment?: string | null;
    pmHorseBodyComment?: string | null;
    amHorseBodyCare?: string | null;
    pmHorseBodyCare?: string | null;
    freeComment?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseBodyRecordhorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseBodyRecordInput;
};
type HorseBodyRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseBodyRecordFactoryDefineInput, never>>;
type HorseBodyRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseBodyRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseBodyRecord, Prisma.HorseBodyRecordCreateInput, TTransients>;
type HorseBodyRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseBodyRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseBodyRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseBodyRecord, Prisma.HorseBodyRecordCreateInput, TTransients>;
type HorseBodyRecordTraitKeys<TOptions extends HorseBodyRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseBodyRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseBodyRecord";
    build(inputData?: Partial<Prisma.HorseBodyRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseBodyRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseBodyRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseBodyRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseBodyRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseBodyRecordCreateInput[]>;
    pickForConnect(inputData: HorseBodyRecord): Pick<HorseBodyRecord, "horseBodyRecordId">;
    create(inputData?: Partial<Prisma.HorseBodyRecordCreateInput & TTransients>): PromiseLike<HorseBodyRecord>;
    createList(list: readonly Partial<Prisma.HorseBodyRecordCreateInput & TTransients>[]): PromiseLike<HorseBodyRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseBodyRecordCreateInput & TTransients>): PromiseLike<HorseBodyRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseBodyRecordCreateInput & TTransients>): PromiseLike<Pick<HorseBodyRecord, "horseBodyRecordId">>;
}
export interface HorseBodyRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseBodyRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseBodyRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseBodyRecordFactoryBuilder {
    <TOptions extends HorseBodyRecordFactoryDefineOptions>(options: TOptions): HorseBodyRecordFactoryInterface<{}, HorseBodyRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseBodyRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseBodyRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseBodyRecordFactoryInterface<TTransients, HorseBodyRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseBodyRecord} model.
 *
 * @param options
 * @returns factory {@link HorseBodyRecordFactoryInterface}
 */
export declare const defineHorseBodyRecordFactory: HorseBodyRecordFactoryBuilder;
type HorseTrainingRecordhorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseTrainingRecordsInput["create"]>;
};
type HorseTrainingRecordriderFactory = {
    _factoryFor: "Staff";
    build: () => PromiseLike<Prisma.StaffCreateNestedOneWithoutHorseTrainingRecordsInput["create"]>;
};
type HorseTrainingRecordtrainingMenuFactory = {
    _factoryFor: "TrainingMenu";
    build: () => PromiseLike<Prisma.TrainingMenuCreateNestedOneWithoutHorseTrainingRecordsInput["create"]>;
};
type HorseTrainingRecordfacilityFactory = {
    _factoryFor: "Facility";
    build: () => PromiseLike<Prisma.FacilityCreateNestedOneWithoutHorseTrainingRecordsInput["create"]>;
};
type HorseTrainingRecordtrainingCourseFactory = {
    _factoryFor: "TrainingCourse";
    build: () => PromiseLike<Prisma.TrainingCourseCreateNestedOneWithoutHorseTrainingRecordsInput["create"]>;
};
type HorseTrainingRecordFactoryDefineInput = {
    trainingRecordUuid?: Buffer;
    trainingRecordInternalId?: (bigint | number);
    isGaitAbnormal?: boolean | null;
    gaitAbnormalDescription?: string | null;
    trainingType?: string | null;
    gateTrainingType?: string | null;
    facilityName?: string | null;
    courseGoing?: string | null;
    furlongTime?: string | null;
    furlongTimePosition?: string | null;
    poolTrainingType?: string | null;
    lactateLevel?: number | null;
    trainingComment?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseTrainingRecordhorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseTrainingRecordsInput;
    rider?: HorseTrainingRecordriderFactory | Prisma.StaffCreateNestedOneWithoutHorseTrainingRecordsInput;
    trainingMenu?: HorseTrainingRecordtrainingMenuFactory | Prisma.TrainingMenuCreateNestedOneWithoutHorseTrainingRecordsInput;
    facility?: HorseTrainingRecordfacilityFactory | Prisma.FacilityCreateNestedOneWithoutHorseTrainingRecordsInput;
    trainingCourse?: HorseTrainingRecordtrainingCourseFactory | Prisma.TrainingCourseCreateNestedOneWithoutHorseTrainingRecordsInput;
    trainingPartners?: Prisma.TrainingPartnerCreateNestedManyWithoutHorseTrainingRecordInput;
};
type HorseTrainingRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseTrainingRecordFactoryDefineInput, never>>;
type HorseTrainingRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseTrainingRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseTrainingRecord, Prisma.HorseTrainingRecordCreateInput, TTransients>;
type HorseTrainingRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseTrainingRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseTrainingRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseTrainingRecord, Prisma.HorseTrainingRecordCreateInput, TTransients>;
type HorseTrainingRecordTraitKeys<TOptions extends HorseTrainingRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseTrainingRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseTrainingRecord";
    build(inputData?: Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseTrainingRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseTrainingRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseTrainingRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseTrainingRecordCreateInput[]>;
    pickForConnect(inputData: HorseTrainingRecord): Pick<HorseTrainingRecord, "trainingRecordUuid">;
    create(inputData?: Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>): PromiseLike<HorseTrainingRecord>;
    createList(list: readonly Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>[]): PromiseLike<HorseTrainingRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>): PromiseLike<HorseTrainingRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseTrainingRecordCreateInput & TTransients>): PromiseLike<Pick<HorseTrainingRecord, "trainingRecordUuid">>;
}
export interface HorseTrainingRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseTrainingRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseTrainingRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseTrainingRecordFactoryBuilder {
    <TOptions extends HorseTrainingRecordFactoryDefineOptions>(options: TOptions): HorseTrainingRecordFactoryInterface<{}, HorseTrainingRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseTrainingRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseTrainingRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseTrainingRecordFactoryInterface<TTransients, HorseTrainingRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseTrainingRecord} model.
 *
 * @param options
 * @returns factory {@link HorseTrainingRecordFactoryInterface}
 */
export declare const defineHorseTrainingRecordFactory: HorseTrainingRecordFactoryBuilder;
type TrainingPartnerhorseTrainingRecordFactory = {
    _factoryFor: "HorseTrainingRecord";
    build: () => PromiseLike<Prisma.HorseTrainingRecordCreateNestedOneWithoutTrainingPartnersInput["create"]>;
};
type TrainingPartnerhorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutTrainingPartnersInput["create"]>;
};
type TrainingPartnerFactoryDefineInput = {
    trainingPartnerId?: Buffer;
    rank?: number | null;
    horseName?: string;
    trackPosition?: string | null;
    startingOrder?: number | null;
    detail?: string | null;
    intensity?: string | null;
    margin?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseTrainingRecord: TrainingPartnerhorseTrainingRecordFactory | Prisma.HorseTrainingRecordCreateNestedOneWithoutTrainingPartnersInput;
    horse?: TrainingPartnerhorseFactory | Prisma.HorseCreateNestedOneWithoutTrainingPartnersInput;
};
type TrainingPartnerTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingPartnerFactoryDefineInput, never>>;
type TrainingPartnerFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingPartnerFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingPartner, Prisma.TrainingPartnerCreateInput, TTransients>;
type TrainingPartnerFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<TrainingPartnerFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: TrainingPartnerFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingPartner, Prisma.TrainingPartnerCreateInput, TTransients>;
type TrainingPartnerTraitKeys<TOptions extends TrainingPartnerFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingPartnerFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingPartner";
    build(inputData?: Partial<Prisma.TrainingPartnerCreateInput & TTransients>): PromiseLike<Prisma.TrainingPartnerCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingPartnerCreateInput & TTransients>): PromiseLike<Prisma.TrainingPartnerCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingPartnerCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingPartnerCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingPartnerCreateInput & TTransients>): PromiseLike<Prisma.TrainingPartnerCreateInput[]>;
    pickForConnect(inputData: TrainingPartner): Pick<TrainingPartner, "trainingPartnerId">;
    create(inputData?: Partial<Prisma.TrainingPartnerCreateInput & TTransients>): PromiseLike<TrainingPartner>;
    createList(list: readonly Partial<Prisma.TrainingPartnerCreateInput & TTransients>[]): PromiseLike<TrainingPartner[]>;
    createList(count: number, item?: Partial<Prisma.TrainingPartnerCreateInput & TTransients>): PromiseLike<TrainingPartner[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingPartnerCreateInput & TTransients>): PromiseLike<Pick<TrainingPartner, "trainingPartnerId">>;
}
export interface TrainingPartnerFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingPartnerFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingPartnerFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingPartnerFactoryBuilder {
    <TOptions extends TrainingPartnerFactoryDefineOptions>(options: TOptions): TrainingPartnerFactoryInterface<{}, TrainingPartnerTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingPartnerTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingPartnerFactoryDefineOptions<TTransients>>(options: TOptions) => TrainingPartnerFactoryInterface<TTransients, TrainingPartnerTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingPartner} model.
 *
 * @param options
 * @returns factory {@link TrainingPartnerFactoryInterface}
 */
export declare const defineTrainingPartnerFactory: TrainingPartnerFactoryBuilder;
type OrganizationVeterinarianorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutOrganizationVeterinariansInput["create"]>;
};
type OrganizationVeterinarianFactoryDefineInput = {
    organizationVeterinarianId?: Buffer;
    veterinarianName?: string;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseMedicalTreatmentRecords?: Prisma.HorseMedicalTreatmentRecordCreateNestedManyWithoutVeterinarianInput;
    organization: OrganizationVeterinarianorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutOrganizationVeterinariansInput;
};
type OrganizationVeterinarianTransientFields = Record<string, unknown> & Partial<Record<keyof OrganizationVeterinarianFactoryDefineInput, never>>;
type OrganizationVeterinarianFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OrganizationVeterinarianFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<OrganizationVeterinarian, Prisma.OrganizationVeterinarianCreateInput, TTransients>;
type OrganizationVeterinarianFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<OrganizationVeterinarianFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: OrganizationVeterinarianFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<OrganizationVeterinarian, Prisma.OrganizationVeterinarianCreateInput, TTransients>;
type OrganizationVeterinarianTraitKeys<TOptions extends OrganizationVeterinarianFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OrganizationVeterinarianFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "OrganizationVeterinarian";
    build(inputData?: Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>): PromiseLike<Prisma.OrganizationVeterinarianCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>): PromiseLike<Prisma.OrganizationVeterinarianCreateInput>;
    buildList(list: readonly Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>[]): PromiseLike<Prisma.OrganizationVeterinarianCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>): PromiseLike<Prisma.OrganizationVeterinarianCreateInput[]>;
    pickForConnect(inputData: OrganizationVeterinarian): Pick<OrganizationVeterinarian, "organizationVeterinarianId">;
    create(inputData?: Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>): PromiseLike<OrganizationVeterinarian>;
    createList(list: readonly Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>[]): PromiseLike<OrganizationVeterinarian[]>;
    createList(count: number, item?: Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>): PromiseLike<OrganizationVeterinarian[]>;
    createForConnect(inputData?: Partial<Prisma.OrganizationVeterinarianCreateInput & TTransients>): PromiseLike<Pick<OrganizationVeterinarian, "organizationVeterinarianId">>;
}
export interface OrganizationVeterinarianFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OrganizationVeterinarianFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OrganizationVeterinarianFactoryInterfaceWithoutTraits<TTransients>;
}
interface OrganizationVeterinarianFactoryBuilder {
    <TOptions extends OrganizationVeterinarianFactoryDefineOptions>(options: TOptions): OrganizationVeterinarianFactoryInterface<{}, OrganizationVeterinarianTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OrganizationVeterinarianTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OrganizationVeterinarianFactoryDefineOptions<TTransients>>(options: TOptions) => OrganizationVeterinarianFactoryInterface<TTransients, OrganizationVeterinarianTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link OrganizationVeterinarian} model.
 *
 * @param options
 * @returns factory {@link OrganizationVeterinarianFactoryInterface}
 */
export declare const defineOrganizationVeterinarianFactory: OrganizationVeterinarianFactoryBuilder;
type OrganizationFarrierorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutOrganizationFarriersInput["create"]>;
};
type OrganizationFarrierFactoryDefineInput = {
    organizationFarrierId?: Buffer;
    farrierName?: string;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseShoeingRecords?: Prisma.HorseShoeingRecordCreateNestedManyWithoutFarrierInput;
    organization: OrganizationFarrierorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutOrganizationFarriersInput;
};
type OrganizationFarrierTransientFields = Record<string, unknown> & Partial<Record<keyof OrganizationFarrierFactoryDefineInput, never>>;
type OrganizationFarrierFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OrganizationFarrierFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<OrganizationFarrier, Prisma.OrganizationFarrierCreateInput, TTransients>;
type OrganizationFarrierFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<OrganizationFarrierFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: OrganizationFarrierFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<OrganizationFarrier, Prisma.OrganizationFarrierCreateInput, TTransients>;
type OrganizationFarrierTraitKeys<TOptions extends OrganizationFarrierFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OrganizationFarrierFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "OrganizationFarrier";
    build(inputData?: Partial<Prisma.OrganizationFarrierCreateInput & TTransients>): PromiseLike<Prisma.OrganizationFarrierCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OrganizationFarrierCreateInput & TTransients>): PromiseLike<Prisma.OrganizationFarrierCreateInput>;
    buildList(list: readonly Partial<Prisma.OrganizationFarrierCreateInput & TTransients>[]): PromiseLike<Prisma.OrganizationFarrierCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OrganizationFarrierCreateInput & TTransients>): PromiseLike<Prisma.OrganizationFarrierCreateInput[]>;
    pickForConnect(inputData: OrganizationFarrier): Pick<OrganizationFarrier, "organizationFarrierId">;
    create(inputData?: Partial<Prisma.OrganizationFarrierCreateInput & TTransients>): PromiseLike<OrganizationFarrier>;
    createList(list: readonly Partial<Prisma.OrganizationFarrierCreateInput & TTransients>[]): PromiseLike<OrganizationFarrier[]>;
    createList(count: number, item?: Partial<Prisma.OrganizationFarrierCreateInput & TTransients>): PromiseLike<OrganizationFarrier[]>;
    createForConnect(inputData?: Partial<Prisma.OrganizationFarrierCreateInput & TTransients>): PromiseLike<Pick<OrganizationFarrier, "organizationFarrierId">>;
}
export interface OrganizationFarrierFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OrganizationFarrierFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OrganizationFarrierFactoryInterfaceWithoutTraits<TTransients>;
}
interface OrganizationFarrierFactoryBuilder {
    <TOptions extends OrganizationFarrierFactoryDefineOptions>(options: TOptions): OrganizationFarrierFactoryInterface<{}, OrganizationFarrierTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OrganizationFarrierTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OrganizationFarrierFactoryDefineOptions<TTransients>>(options: TOptions) => OrganizationFarrierFactoryInterface<TTransients, OrganizationFarrierTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link OrganizationFarrier} model.
 *
 * @param options
 * @returns factory {@link OrganizationFarrierFactoryInterface}
 */
export declare const defineOrganizationFarrierFactory: OrganizationFarrierFactoryBuilder;
type HorseMedicalTreatmentRecordhorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseMedicalTreatmentRecordsInput["create"]>;
};
type HorseMedicalTreatmentRecordveterinarianFactory = {
    _factoryFor: "OrganizationVeterinarian";
    build: () => PromiseLike<Prisma.OrganizationVeterinarianCreateNestedOneWithoutHorseMedicalTreatmentRecordsInput["create"]>;
};
type HorseMedicalTreatmentRecordFactoryDefineInput = {
    horseMedicalTreatmentRecordId?: Buffer;
    horseMedicalTreatmentReason?: string | null;
    horseMedicalTreatmentInspection?: string | null;
    horseMedicalTreatmentResult?: string | null;
    horseMedicalTreatmentDetail?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseMedicalTreatmentRecordhorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseMedicalTreatmentRecordsInput;
    veterinarian?: HorseMedicalTreatmentRecordveterinarianFactory | Prisma.OrganizationVeterinarianCreateNestedOneWithoutHorseMedicalTreatmentRecordsInput;
    horseMedicalTreatmentInvoicePhotos?: Prisma.HorseMedicalTreatmentInvoicePhotoCreateNestedManyWithoutHorseMedicalTreatmentRecordInput;
    horseMedicalTreatmentAffectedAreaPhotos?: Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateNestedManyWithoutHorseMedicalTreatmentRecordInput;
};
type HorseMedicalTreatmentRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseMedicalTreatmentRecordFactoryDefineInput, never>>;
type HorseMedicalTreatmentRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseMedicalTreatmentRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseMedicalTreatmentRecord, Prisma.HorseMedicalTreatmentRecordCreateInput, TTransients>;
type HorseMedicalTreatmentRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseMedicalTreatmentRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseMedicalTreatmentRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseMedicalTreatmentRecord, Prisma.HorseMedicalTreatmentRecordCreateInput, TTransients>;
type HorseMedicalTreatmentRecordTraitKeys<TOptions extends HorseMedicalTreatmentRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseMedicalTreatmentRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseMedicalTreatmentRecord";
    build(inputData?: Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseMedicalTreatmentRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentRecordCreateInput[]>;
    pickForConnect(inputData: HorseMedicalTreatmentRecord): Pick<HorseMedicalTreatmentRecord, "horseMedicalTreatmentRecordId">;
    create(inputData?: Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<HorseMedicalTreatmentRecord>;
    createList(list: readonly Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>[]): PromiseLike<HorseMedicalTreatmentRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<HorseMedicalTreatmentRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Pick<HorseMedicalTreatmentRecord, "horseMedicalTreatmentRecordId">>;
}
export interface HorseMedicalTreatmentRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseMedicalTreatmentRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseMedicalTreatmentRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseMedicalTreatmentRecordFactoryBuilder {
    <TOptions extends HorseMedicalTreatmentRecordFactoryDefineOptions>(options: TOptions): HorseMedicalTreatmentRecordFactoryInterface<{}, HorseMedicalTreatmentRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseMedicalTreatmentRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseMedicalTreatmentRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseMedicalTreatmentRecordFactoryInterface<TTransients, HorseMedicalTreatmentRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseMedicalTreatmentRecord} model.
 *
 * @param options
 * @returns factory {@link HorseMedicalTreatmentRecordFactoryInterface}
 */
export declare const defineHorseMedicalTreatmentRecordFactory: HorseMedicalTreatmentRecordFactoryBuilder;
type HorseMedicalTreatmentInvoicePhotohorseMedicalTreatmentRecordFactory = {
    _factoryFor: "HorseMedicalTreatmentRecord";
    build: () => PromiseLike<Prisma.HorseMedicalTreatmentRecordCreateNestedOneWithoutHorseMedicalTreatmentInvoicePhotosInput["create"]>;
};
type HorseMedicalTreatmentInvoicePhotoFactoryDefineInput = {
    horseMedicalTreatmentRecordInvoicePhotoId?: Buffer;
    horseMedicalTreatmentRecordInvoicePhotoInternalId?: (bigint | number);
    photoPath?: string;
    createdAt?: Date;
    updatedAt?: Date;
    horseMedicalTreatmentRecord: HorseMedicalTreatmentInvoicePhotohorseMedicalTreatmentRecordFactory | Prisma.HorseMedicalTreatmentRecordCreateNestedOneWithoutHorseMedicalTreatmentInvoicePhotosInput;
};
type HorseMedicalTreatmentInvoicePhotoTransientFields = Record<string, unknown> & Partial<Record<keyof HorseMedicalTreatmentInvoicePhotoFactoryDefineInput, never>>;
type HorseMedicalTreatmentInvoicePhotoFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseMedicalTreatmentInvoicePhotoFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseMedicalTreatmentInvoicePhoto, Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput, TTransients>;
type HorseMedicalTreatmentInvoicePhotoFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseMedicalTreatmentInvoicePhotoFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseMedicalTreatmentInvoicePhotoFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseMedicalTreatmentInvoicePhoto, Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput, TTransients>;
type HorseMedicalTreatmentInvoicePhotoTraitKeys<TOptions extends HorseMedicalTreatmentInvoicePhotoFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseMedicalTreatmentInvoicePhotoFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseMedicalTreatmentInvoicePhoto";
    build(inputData?: Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>[]): PromiseLike<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput[]>;
    pickForConnect(inputData: HorseMedicalTreatmentInvoicePhoto): Pick<HorseMedicalTreatmentInvoicePhoto, "horseMedicalTreatmentRecordInvoicePhotoId">;
    create(inputData?: Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>): PromiseLike<HorseMedicalTreatmentInvoicePhoto>;
    createList(list: readonly Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>[]): PromiseLike<HorseMedicalTreatmentInvoicePhoto[]>;
    createList(count: number, item?: Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>): PromiseLike<HorseMedicalTreatmentInvoicePhoto[]>;
    createForConnect(inputData?: Partial<Prisma.HorseMedicalTreatmentInvoicePhotoCreateInput & TTransients>): PromiseLike<Pick<HorseMedicalTreatmentInvoicePhoto, "horseMedicalTreatmentRecordInvoicePhotoId">>;
}
export interface HorseMedicalTreatmentInvoicePhotoFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseMedicalTreatmentInvoicePhotoFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseMedicalTreatmentInvoicePhotoFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseMedicalTreatmentInvoicePhotoFactoryBuilder {
    <TOptions extends HorseMedicalTreatmentInvoicePhotoFactoryDefineOptions>(options: TOptions): HorseMedicalTreatmentInvoicePhotoFactoryInterface<{}, HorseMedicalTreatmentInvoicePhotoTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseMedicalTreatmentInvoicePhotoTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseMedicalTreatmentInvoicePhotoFactoryDefineOptions<TTransients>>(options: TOptions) => HorseMedicalTreatmentInvoicePhotoFactoryInterface<TTransients, HorseMedicalTreatmentInvoicePhotoTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseMedicalTreatmentInvoicePhoto} model.
 *
 * @param options
 * @returns factory {@link HorseMedicalTreatmentInvoicePhotoFactoryInterface}
 */
export declare const defineHorseMedicalTreatmentInvoicePhotoFactory: HorseMedicalTreatmentInvoicePhotoFactoryBuilder;
type HorseMedicalTreatmentAffectedAreaPhotohorseMedicalTreatmentRecordFactory = {
    _factoryFor: "HorseMedicalTreatmentRecord";
    build: () => PromiseLike<Prisma.HorseMedicalTreatmentRecordCreateNestedOneWithoutHorseMedicalTreatmentAffectedAreaPhotosInput["create"]>;
};
type HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineInput = {
    horseMedicalTreatmentAffectedAreaPhotoId?: Buffer;
    horseMedicalTreatmentAffectedAreaPhotoInternalId?: (bigint | number);
    photoPath?: string;
    createdAt?: Date;
    updatedAt?: Date;
    horseMedicalTreatmentRecord: HorseMedicalTreatmentAffectedAreaPhotohorseMedicalTreatmentRecordFactory | Prisma.HorseMedicalTreatmentRecordCreateNestedOneWithoutHorseMedicalTreatmentAffectedAreaPhotosInput;
};
type HorseMedicalTreatmentAffectedAreaPhotoTransientFields = Record<string, unknown> & Partial<Record<keyof HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineInput, never>>;
type HorseMedicalTreatmentAffectedAreaPhotoFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseMedicalTreatmentAffectedAreaPhoto, Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput, TTransients>;
type HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseMedicalTreatmentAffectedAreaPhotoFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseMedicalTreatmentAffectedAreaPhoto, Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput, TTransients>;
type HorseMedicalTreatmentAffectedAreaPhotoTraitKeys<TOptions extends HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseMedicalTreatmentAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseMedicalTreatmentAffectedAreaPhoto";
    build(inputData?: Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>[]): PromiseLike<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput[]>;
    pickForConnect(inputData: HorseMedicalTreatmentAffectedAreaPhoto): Pick<HorseMedicalTreatmentAffectedAreaPhoto, "horseMedicalTreatmentAffectedAreaPhotoId">;
    create(inputData?: Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<HorseMedicalTreatmentAffectedAreaPhoto>;
    createList(list: readonly Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>[]): PromiseLike<HorseMedicalTreatmentAffectedAreaPhoto[]>;
    createList(count: number, item?: Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<HorseMedicalTreatmentAffectedAreaPhoto[]>;
    createForConnect(inputData?: Partial<Prisma.HorseMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Pick<HorseMedicalTreatmentAffectedAreaPhoto, "horseMedicalTreatmentAffectedAreaPhotoId">>;
}
export interface HorseMedicalTreatmentAffectedAreaPhotoFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseMedicalTreatmentAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseMedicalTreatmentAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseMedicalTreatmentAffectedAreaPhotoFactoryBuilder {
    <TOptions extends HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions>(options: TOptions): HorseMedicalTreatmentAffectedAreaPhotoFactoryInterface<{}, HorseMedicalTreatmentAffectedAreaPhotoTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseMedicalTreatmentAffectedAreaPhotoTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions<TTransients>>(options: TOptions) => HorseMedicalTreatmentAffectedAreaPhotoFactoryInterface<TTransients, HorseMedicalTreatmentAffectedAreaPhotoTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseMedicalTreatmentAffectedAreaPhoto} model.
 *
 * @param options
 * @returns factory {@link HorseMedicalTreatmentAffectedAreaPhotoFactoryInterface}
 */
export declare const defineHorseMedicalTreatmentAffectedAreaPhotoFactory: HorseMedicalTreatmentAffectedAreaPhotoFactoryBuilder;
type HorseShoeingRecordhorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseShoeingRecordsInput["create"]>;
};
type HorseShoeingRecordfarrierFactory = {
    _factoryFor: "OrganizationFarrier";
    build: () => PromiseLike<Prisma.OrganizationFarrierCreateNestedOneWithoutHorseShoeingRecordsInput["create"]>;
};
type HorseShoeingRecordFactoryDefineInput = {
    horseShoeingRecordId?: Buffer;
    horseShoeingTreatmentType?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    comment?: string | null;
    horseDailyRecord: HorseShoeingRecordhorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseShoeingRecordsInput;
    farrier?: HorseShoeingRecordfarrierFactory | Prisma.OrganizationFarrierCreateNestedOneWithoutHorseShoeingRecordsInput;
    horseShoeingInvoicePhotos?: Prisma.HorseShoeingInvoicePhotoCreateNestedManyWithoutHorseShoeingRecordInput;
};
type HorseShoeingRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseShoeingRecordFactoryDefineInput, never>>;
type HorseShoeingRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseShoeingRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseShoeingRecord, Prisma.HorseShoeingRecordCreateInput, TTransients>;
type HorseShoeingRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseShoeingRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseShoeingRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseShoeingRecord, Prisma.HorseShoeingRecordCreateInput, TTransients>;
type HorseShoeingRecordTraitKeys<TOptions extends HorseShoeingRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseShoeingRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseShoeingRecord";
    build(inputData?: Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseShoeingRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseShoeingRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseShoeingRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseShoeingRecordCreateInput[]>;
    pickForConnect(inputData: HorseShoeingRecord): Pick<HorseShoeingRecord, "horseShoeingRecordId">;
    create(inputData?: Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>): PromiseLike<HorseShoeingRecord>;
    createList(list: readonly Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>[]): PromiseLike<HorseShoeingRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>): PromiseLike<HorseShoeingRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseShoeingRecordCreateInput & TTransients>): PromiseLike<Pick<HorseShoeingRecord, "horseShoeingRecordId">>;
}
export interface HorseShoeingRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseShoeingRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseShoeingRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseShoeingRecordFactoryBuilder {
    <TOptions extends HorseShoeingRecordFactoryDefineOptions>(options: TOptions): HorseShoeingRecordFactoryInterface<{}, HorseShoeingRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseShoeingRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseShoeingRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseShoeingRecordFactoryInterface<TTransients, HorseShoeingRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseShoeingRecord} model.
 *
 * @param options
 * @returns factory {@link HorseShoeingRecordFactoryInterface}
 */
export declare const defineHorseShoeingRecordFactory: HorseShoeingRecordFactoryBuilder;
type HorseShoeingInvoicePhotohorseShoeingRecordFactory = {
    _factoryFor: "HorseShoeingRecord";
    build: () => PromiseLike<Prisma.HorseShoeingRecordCreateNestedOneWithoutHorseShoeingInvoicePhotosInput["create"]>;
};
type HorseShoeingInvoicePhotoFactoryDefineInput = {
    horseShoeingInvoicePhotoId?: Buffer;
    horseShoeingInvoicePhotoInternalId?: (bigint | number);
    photoPath?: string;
    createdAt?: Date;
    updatedAt?: Date;
    horseShoeingRecord: HorseShoeingInvoicePhotohorseShoeingRecordFactory | Prisma.HorseShoeingRecordCreateNestedOneWithoutHorseShoeingInvoicePhotosInput;
};
type HorseShoeingInvoicePhotoTransientFields = Record<string, unknown> & Partial<Record<keyof HorseShoeingInvoicePhotoFactoryDefineInput, never>>;
type HorseShoeingInvoicePhotoFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseShoeingInvoicePhotoFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseShoeingInvoicePhoto, Prisma.HorseShoeingInvoicePhotoCreateInput, TTransients>;
type HorseShoeingInvoicePhotoFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseShoeingInvoicePhotoFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseShoeingInvoicePhotoFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseShoeingInvoicePhoto, Prisma.HorseShoeingInvoicePhotoCreateInput, TTransients>;
type HorseShoeingInvoicePhotoTraitKeys<TOptions extends HorseShoeingInvoicePhotoFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseShoeingInvoicePhotoFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseShoeingInvoicePhoto";
    build(inputData?: Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseShoeingInvoicePhotoCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseShoeingInvoicePhotoCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>[]): PromiseLike<Prisma.HorseShoeingInvoicePhotoCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>): PromiseLike<Prisma.HorseShoeingInvoicePhotoCreateInput[]>;
    pickForConnect(inputData: HorseShoeingInvoicePhoto): Pick<HorseShoeingInvoicePhoto, "horseShoeingInvoicePhotoId">;
    create(inputData?: Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>): PromiseLike<HorseShoeingInvoicePhoto>;
    createList(list: readonly Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>[]): PromiseLike<HorseShoeingInvoicePhoto[]>;
    createList(count: number, item?: Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>): PromiseLike<HorseShoeingInvoicePhoto[]>;
    createForConnect(inputData?: Partial<Prisma.HorseShoeingInvoicePhotoCreateInput & TTransients>): PromiseLike<Pick<HorseShoeingInvoicePhoto, "horseShoeingInvoicePhotoId">>;
}
export interface HorseShoeingInvoicePhotoFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseShoeingInvoicePhotoFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseShoeingInvoicePhotoFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseShoeingInvoicePhotoFactoryBuilder {
    <TOptions extends HorseShoeingInvoicePhotoFactoryDefineOptions>(options: TOptions): HorseShoeingInvoicePhotoFactoryInterface<{}, HorseShoeingInvoicePhotoTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseShoeingInvoicePhotoTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseShoeingInvoicePhotoFactoryDefineOptions<TTransients>>(options: TOptions) => HorseShoeingInvoicePhotoFactoryInterface<TTransients, HorseShoeingInvoicePhotoTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseShoeingInvoicePhoto} model.
 *
 * @param options
 * @returns factory {@link HorseShoeingInvoicePhotoFactoryInterface}
 */
export declare const defineHorseShoeingInvoicePhotoFactory: HorseShoeingInvoicePhotoFactoryBuilder;
type HorseRaceResultRecordhorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseRaceResultRecordInput["create"]>;
};
type HorseRaceResultRecordracePlaceFactory = {
    _factoryFor: "RacePlace";
    build: () => PromiseLike<Prisma.RacePlaceCreateNestedOneWithoutHorseRaceResultRecordsInput["create"]>;
};
type HorseRaceResultRecordFactoryDefineInput = {
    raceResultId?: Buffer;
    raceResultInternalId?: (bigint | number);
    raceName?: string | null;
    raceNumber?: number | null;
    distance?: number | null;
    going?: string | null;
    trackType?: string | null;
    jockeyName?: string | null;
    weight?: number | null;
    beforeRaceWeightDiff?: number | null;
    rank?: number | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseRaceResultRecordhorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseRaceResultRecordInput;
    racePlace?: HorseRaceResultRecordracePlaceFactory | Prisma.RacePlaceCreateNestedOneWithoutHorseRaceResultRecordsInput;
};
type HorseRaceResultRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseRaceResultRecordFactoryDefineInput, never>>;
type HorseRaceResultRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseRaceResultRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseRaceResultRecord, Prisma.HorseRaceResultRecordCreateInput, TTransients>;
type HorseRaceResultRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseRaceResultRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseRaceResultRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseRaceResultRecord, Prisma.HorseRaceResultRecordCreateInput, TTransients>;
type HorseRaceResultRecordTraitKeys<TOptions extends HorseRaceResultRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseRaceResultRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseRaceResultRecord";
    build(inputData?: Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseRaceResultRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseRaceResultRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseRaceResultRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseRaceResultRecordCreateInput[]>;
    pickForConnect(inputData: HorseRaceResultRecord): Pick<HorseRaceResultRecord, "raceResultId">;
    create(inputData?: Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>): PromiseLike<HorseRaceResultRecord>;
    createList(list: readonly Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>[]): PromiseLike<HorseRaceResultRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>): PromiseLike<HorseRaceResultRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseRaceResultRecordCreateInput & TTransients>): PromiseLike<Pick<HorseRaceResultRecord, "raceResultId">>;
}
export interface HorseRaceResultRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseRaceResultRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseRaceResultRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseRaceResultRecordFactoryBuilder {
    <TOptions extends HorseRaceResultRecordFactoryDefineOptions>(options: TOptions): HorseRaceResultRecordFactoryInterface<{}, HorseRaceResultRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseRaceResultRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseRaceResultRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseRaceResultRecordFactoryInterface<TTransients, HorseRaceResultRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseRaceResultRecord} model.
 *
 * @param options
 * @returns factory {@link HorseRaceResultRecordFactoryInterface}
 */
export declare const defineHorseRaceResultRecordFactory: HorseRaceResultRecordFactoryBuilder;
type HorseRaceRecapRecordhorseDailyRecordFactory = {
    _factoryFor: "HorseDailyRecord";
    build: () => PromiseLike<Prisma.HorseDailyRecordCreateNestedOneWithoutHorseRaceRecapRecordInput["create"]>;
};
type HorseRaceRecapRecordstaffFactory = {
    _factoryFor: "Staff";
    build: () => PromiseLike<Prisma.StaffCreateNestedOneWithoutHorseRaceRecapRecordsInput["create"]>;
};
type HorseRaceRecapRecordFactoryDefineInput = {
    raceRecapId?: Buffer;
    raceRecapInternalId?: (bigint | number);
    attendance?: string | null;
    equipment?: string | null;
    transportComment?: string | null;
    stallComment?: string | null;
    paddockComment?: string | null;
    warmUpComment?: string | null;
    gateComment?: string | null;
    raceStrategyComment?: string | null;
    afterRaceComment?: string | null;
    jockeyComment?: string | null;
    trainerComment?: string | null;
    nextRaceComment?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horseDailyRecord: HorseRaceRecapRecordhorseDailyRecordFactory | Prisma.HorseDailyRecordCreateNestedOneWithoutHorseRaceRecapRecordInput;
    staff?: HorseRaceRecapRecordstaffFactory | Prisma.StaffCreateNestedOneWithoutHorseRaceRecapRecordsInput;
};
type HorseRaceRecapRecordTransientFields = Record<string, unknown> & Partial<Record<keyof HorseRaceRecapRecordFactoryDefineInput, never>>;
type HorseRaceRecapRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseRaceRecapRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseRaceRecapRecord, Prisma.HorseRaceRecapRecordCreateInput, TTransients>;
type HorseRaceRecapRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseRaceRecapRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseRaceRecapRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseRaceRecapRecord, Prisma.HorseRaceRecapRecordCreateInput, TTransients>;
type HorseRaceRecapRecordTraitKeys<TOptions extends HorseRaceRecapRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseRaceRecapRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseRaceRecapRecord";
    build(inputData?: Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseRaceRecapRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseRaceRecapRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>[]): PromiseLike<Prisma.HorseRaceRecapRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>): PromiseLike<Prisma.HorseRaceRecapRecordCreateInput[]>;
    pickForConnect(inputData: HorseRaceRecapRecord): Pick<HorseRaceRecapRecord, "raceRecapId">;
    create(inputData?: Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>): PromiseLike<HorseRaceRecapRecord>;
    createList(list: readonly Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>[]): PromiseLike<HorseRaceRecapRecord[]>;
    createList(count: number, item?: Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>): PromiseLike<HorseRaceRecapRecord[]>;
    createForConnect(inputData?: Partial<Prisma.HorseRaceRecapRecordCreateInput & TTransients>): PromiseLike<Pick<HorseRaceRecapRecord, "raceRecapId">>;
}
export interface HorseRaceRecapRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseRaceRecapRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseRaceRecapRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseRaceRecapRecordFactoryBuilder {
    <TOptions extends HorseRaceRecapRecordFactoryDefineOptions>(options: TOptions): HorseRaceRecapRecordFactoryInterface<{}, HorseRaceRecapRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseRaceRecapRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseRaceRecapRecordFactoryDefineOptions<TTransients>>(options: TOptions) => HorseRaceRecapRecordFactoryInterface<TTransients, HorseRaceRecapRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseRaceRecapRecord} model.
 *
 * @param options
 * @returns factory {@link HorseRaceRecapRecordFactoryInterface}
 */
export declare const defineHorseRaceRecapRecordFactory: HorseRaceRecapRecordFactoryBuilder;
type RacePlaceFactoryDefineInput = {
    name?: string;
    fullName?: string | null;
    code?: string | null;
    region?: string | null;
    horseRaceResultRecords?: Prisma.HorseRaceResultRecordCreateNestedManyWithoutRacePlaceInput;
};
type RacePlaceTransientFields = Record<string, unknown> & Partial<Record<keyof RacePlaceFactoryDefineInput, never>>;
type RacePlaceFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<RacePlaceFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<RacePlace, Prisma.RacePlaceCreateInput, TTransients>;
type RacePlaceFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<RacePlaceFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: RacePlaceFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<RacePlace, Prisma.RacePlaceCreateInput, TTransients>;
type RacePlaceTraitKeys<TOptions extends RacePlaceFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface RacePlaceFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "RacePlace";
    build(inputData?: Partial<Prisma.RacePlaceCreateInput & TTransients>): PromiseLike<Prisma.RacePlaceCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.RacePlaceCreateInput & TTransients>): PromiseLike<Prisma.RacePlaceCreateInput>;
    buildList(list: readonly Partial<Prisma.RacePlaceCreateInput & TTransients>[]): PromiseLike<Prisma.RacePlaceCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.RacePlaceCreateInput & TTransients>): PromiseLike<Prisma.RacePlaceCreateInput[]>;
    pickForConnect(inputData: RacePlace): Pick<RacePlace, "racePlaceId">;
    create(inputData?: Partial<Prisma.RacePlaceCreateInput & TTransients>): PromiseLike<RacePlace>;
    createList(list: readonly Partial<Prisma.RacePlaceCreateInput & TTransients>[]): PromiseLike<RacePlace[]>;
    createList(count: number, item?: Partial<Prisma.RacePlaceCreateInput & TTransients>): PromiseLike<RacePlace[]>;
    createForConnect(inputData?: Partial<Prisma.RacePlaceCreateInput & TTransients>): PromiseLike<Pick<RacePlace, "racePlaceId">>;
}
export interface RacePlaceFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends RacePlaceFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): RacePlaceFactoryInterfaceWithoutTraits<TTransients>;
}
interface RacePlaceFactoryBuilder {
    <TOptions extends RacePlaceFactoryDefineOptions>(options?: TOptions): RacePlaceFactoryInterface<{}, RacePlaceTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends RacePlaceTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends RacePlaceFactoryDefineOptions<TTransients>>(options?: TOptions) => RacePlaceFactoryInterface<TTransients, RacePlaceTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link RacePlace} model.
 *
 * @param options
 * @returns factory {@link RacePlaceFactoryInterface}
 */
export declare const defineRacePlaceFactory: RacePlaceFactoryBuilder;
type BusinessTripHistoryorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutBusinessTripHistoriesInput["create"]>;
};
type BusinessTripHistoryFactoryDefineInput = {
    businessTripHistoryId?: Buffer;
    staffName?: string | null;
    destinationName?: string | null;
    startYear?: number | null;
    startMonth?: number | null;
    startDay?: number | null;
    endYear?: number | null;
    endMonth?: number | null;
    endDay?: number | null;
    createdAt?: Date;
    updatedAt?: Date;
    organization: BusinessTripHistoryorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutBusinessTripHistoriesInput;
    horses?: Prisma.BusinessTripHistoryHorseCreateNestedManyWithoutBusinessTripHistoryInput;
};
type BusinessTripHistoryTransientFields = Record<string, unknown> & Partial<Record<keyof BusinessTripHistoryFactoryDefineInput, never>>;
type BusinessTripHistoryFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<BusinessTripHistoryFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<BusinessTripHistory, Prisma.BusinessTripHistoryCreateInput, TTransients>;
type BusinessTripHistoryFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<BusinessTripHistoryFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: BusinessTripHistoryFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<BusinessTripHistory, Prisma.BusinessTripHistoryCreateInput, TTransients>;
type BusinessTripHistoryTraitKeys<TOptions extends BusinessTripHistoryFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface BusinessTripHistoryFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "BusinessTripHistory";
    build(inputData?: Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>): PromiseLike<Prisma.BusinessTripHistoryCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>): PromiseLike<Prisma.BusinessTripHistoryCreateInput>;
    buildList(list: readonly Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>[]): PromiseLike<Prisma.BusinessTripHistoryCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>): PromiseLike<Prisma.BusinessTripHistoryCreateInput[]>;
    pickForConnect(inputData: BusinessTripHistory): Pick<BusinessTripHistory, "businessTripHistoryId">;
    create(inputData?: Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>): PromiseLike<BusinessTripHistory>;
    createList(list: readonly Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>[]): PromiseLike<BusinessTripHistory[]>;
    createList(count: number, item?: Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>): PromiseLike<BusinessTripHistory[]>;
    createForConnect(inputData?: Partial<Prisma.BusinessTripHistoryCreateInput & TTransients>): PromiseLike<Pick<BusinessTripHistory, "businessTripHistoryId">>;
}
export interface BusinessTripHistoryFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends BusinessTripHistoryFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): BusinessTripHistoryFactoryInterfaceWithoutTraits<TTransients>;
}
interface BusinessTripHistoryFactoryBuilder {
    <TOptions extends BusinessTripHistoryFactoryDefineOptions>(options: TOptions): BusinessTripHistoryFactoryInterface<{}, BusinessTripHistoryTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends BusinessTripHistoryTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends BusinessTripHistoryFactoryDefineOptions<TTransients>>(options: TOptions) => BusinessTripHistoryFactoryInterface<TTransients, BusinessTripHistoryTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link BusinessTripHistory} model.
 *
 * @param options
 * @returns factory {@link BusinessTripHistoryFactoryInterface}
 */
export declare const defineBusinessTripHistoryFactory: BusinessTripHistoryFactoryBuilder;
type BusinessTripHistoryHorsebusinessTripHistoryFactory = {
    _factoryFor: "BusinessTripHistory";
    build: () => PromiseLike<Prisma.BusinessTripHistoryCreateNestedOneWithoutHorsesInput["create"]>;
};
type BusinessTripHistoryHorsehorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutBusinessTripHistoriesInput["create"]>;
};
type BusinessTripHistoryHorseFactoryDefineInput = {
    businessTripHistoryHorseId?: Buffer;
    createdAt?: Date;
    updatedAt?: Date;
    businessTripHistory: BusinessTripHistoryHorsebusinessTripHistoryFactory | Prisma.BusinessTripHistoryCreateNestedOneWithoutHorsesInput;
    horse: BusinessTripHistoryHorsehorseFactory | Prisma.HorseCreateNestedOneWithoutBusinessTripHistoriesInput;
};
type BusinessTripHistoryHorseTransientFields = Record<string, unknown> & Partial<Record<keyof BusinessTripHistoryHorseFactoryDefineInput, never>>;
type BusinessTripHistoryHorseFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<BusinessTripHistoryHorseFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<BusinessTripHistoryHorse, Prisma.BusinessTripHistoryHorseCreateInput, TTransients>;
type BusinessTripHistoryHorseFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<BusinessTripHistoryHorseFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: BusinessTripHistoryHorseFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<BusinessTripHistoryHorse, Prisma.BusinessTripHistoryHorseCreateInput, TTransients>;
type BusinessTripHistoryHorseTraitKeys<TOptions extends BusinessTripHistoryHorseFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface BusinessTripHistoryHorseFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "BusinessTripHistoryHorse";
    build(inputData?: Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>): PromiseLike<Prisma.BusinessTripHistoryHorseCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>): PromiseLike<Prisma.BusinessTripHistoryHorseCreateInput>;
    buildList(list: readonly Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>[]): PromiseLike<Prisma.BusinessTripHistoryHorseCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>): PromiseLike<Prisma.BusinessTripHistoryHorseCreateInput[]>;
    pickForConnect(inputData: BusinessTripHistoryHorse): Pick<BusinessTripHistoryHorse, "businessTripHistoryHorseId">;
    create(inputData?: Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>): PromiseLike<BusinessTripHistoryHorse>;
    createList(list: readonly Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>[]): PromiseLike<BusinessTripHistoryHorse[]>;
    createList(count: number, item?: Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>): PromiseLike<BusinessTripHistoryHorse[]>;
    createForConnect(inputData?: Partial<Prisma.BusinessTripHistoryHorseCreateInput & TTransients>): PromiseLike<Pick<BusinessTripHistoryHorse, "businessTripHistoryHorseId">>;
}
export interface BusinessTripHistoryHorseFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends BusinessTripHistoryHorseFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): BusinessTripHistoryHorseFactoryInterfaceWithoutTraits<TTransients>;
}
interface BusinessTripHistoryHorseFactoryBuilder {
    <TOptions extends BusinessTripHistoryHorseFactoryDefineOptions>(options: TOptions): BusinessTripHistoryHorseFactoryInterface<{}, BusinessTripHistoryHorseTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends BusinessTripHistoryHorseTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends BusinessTripHistoryHorseFactoryDefineOptions<TTransients>>(options: TOptions) => BusinessTripHistoryHorseFactoryInterface<TTransients, BusinessTripHistoryHorseTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link BusinessTripHistoryHorse} model.
 *
 * @param options
 * @returns factory {@link BusinessTripHistoryHorseFactoryInterface}
 */
export declare const defineBusinessTripHistoryHorseFactory: BusinessTripHistoryHorseFactoryBuilder;
type FacilityassociationFactory = {
    _factoryFor: "Association";
    build: () => PromiseLike<Prisma.AssociationCreateNestedOneWithoutFacilitiesInput["create"]>;
};
type FacilityFactoryDefineInput = {
    facilityId?: string;
    facilityInternalId?: (bigint | number);
    facilityName?: string;
    createdAt?: Date;
    updatedAt?: Date;
    association: FacilityassociationFactory | Prisma.AssociationCreateNestedOneWithoutFacilitiesInput;
    trainingCourseMasters?: Prisma.TrainingCourseMasterCreateNestedManyWithoutFacilityInput;
    reportSectionWorkoutConditions?: Prisma.ReportSectionWorkoutConditionCreateNestedManyWithoutFacilityInput;
    horseTrainingRecords?: Prisma.HorseTrainingRecordCreateNestedManyWithoutFacilityInput;
    organizations?: Prisma.OrganizationCreateNestedManyWithoutFacilityInput;
};
type FacilityTransientFields = Record<string, unknown> & Partial<Record<keyof FacilityFactoryDefineInput, never>>;
type FacilityFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<FacilityFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Facility, Prisma.FacilityCreateInput, TTransients>;
type FacilityFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<FacilityFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: FacilityFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Facility, Prisma.FacilityCreateInput, TTransients>;
type FacilityTraitKeys<TOptions extends FacilityFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface FacilityFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Facility";
    build(inputData?: Partial<Prisma.FacilityCreateInput & TTransients>): PromiseLike<Prisma.FacilityCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.FacilityCreateInput & TTransients>): PromiseLike<Prisma.FacilityCreateInput>;
    buildList(list: readonly Partial<Prisma.FacilityCreateInput & TTransients>[]): PromiseLike<Prisma.FacilityCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.FacilityCreateInput & TTransients>): PromiseLike<Prisma.FacilityCreateInput[]>;
    pickForConnect(inputData: Facility): Pick<Facility, "facilityId">;
    create(inputData?: Partial<Prisma.FacilityCreateInput & TTransients>): PromiseLike<Facility>;
    createList(list: readonly Partial<Prisma.FacilityCreateInput & TTransients>[]): PromiseLike<Facility[]>;
    createList(count: number, item?: Partial<Prisma.FacilityCreateInput & TTransients>): PromiseLike<Facility[]>;
    createForConnect(inputData?: Partial<Prisma.FacilityCreateInput & TTransients>): PromiseLike<Pick<Facility, "facilityId">>;
}
export interface FacilityFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends FacilityFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): FacilityFactoryInterfaceWithoutTraits<TTransients>;
}
interface FacilityFactoryBuilder {
    <TOptions extends FacilityFactoryDefineOptions>(options: TOptions): FacilityFactoryInterface<{}, FacilityTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends FacilityTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends FacilityFactoryDefineOptions<TTransients>>(options: TOptions) => FacilityFactoryInterface<TTransients, FacilityTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Facility} model.
 *
 * @param options
 * @returns factory {@link FacilityFactoryInterface}
 */
export declare const defineFacilityFactory: FacilityFactoryBuilder;
type TrainingCourseMasterfacilityFactory = {
    _factoryFor: "Facility";
    build: () => PromiseLike<Prisma.FacilityCreateNestedOneWithoutTrainingCourseMastersInput["create"]>;
};
type TrainingCourseMasterFactoryDefineInput = {
    trainingCourseMasterId?: string;
    trainingCourseMasterInternalId?: (bigint | number);
    courseName?: string;
    facilityName?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    tempTrainingFacilityMasterUuid?: Buffer | null;
    trainingCourses?: Prisma.TrainingCourseCreateNestedManyWithoutTrainingCourseMasterInput;
    facility?: TrainingCourseMasterfacilityFactory | Prisma.FacilityCreateNestedOneWithoutTrainingCourseMastersInput;
};
type TrainingCourseMasterTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingCourseMasterFactoryDefineInput, never>>;
type TrainingCourseMasterFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingCourseMasterFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingCourseMaster, Prisma.TrainingCourseMasterCreateInput, TTransients>;
type TrainingCourseMasterFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<TrainingCourseMasterFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: TrainingCourseMasterFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingCourseMaster, Prisma.TrainingCourseMasterCreateInput, TTransients>;
type TrainingCourseMasterTraitKeys<TOptions extends TrainingCourseMasterFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingCourseMasterFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingCourseMaster";
    build(inputData?: Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>): PromiseLike<Prisma.TrainingCourseMasterCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>): PromiseLike<Prisma.TrainingCourseMasterCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingCourseMasterCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>): PromiseLike<Prisma.TrainingCourseMasterCreateInput[]>;
    pickForConnect(inputData: TrainingCourseMaster): Pick<TrainingCourseMaster, "trainingCourseMasterId">;
    create(inputData?: Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>): PromiseLike<TrainingCourseMaster>;
    createList(list: readonly Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>[]): PromiseLike<TrainingCourseMaster[]>;
    createList(count: number, item?: Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>): PromiseLike<TrainingCourseMaster[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingCourseMasterCreateInput & TTransients>): PromiseLike<Pick<TrainingCourseMaster, "trainingCourseMasterId">>;
}
export interface TrainingCourseMasterFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingCourseMasterFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingCourseMasterFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingCourseMasterFactoryBuilder {
    <TOptions extends TrainingCourseMasterFactoryDefineOptions>(options?: TOptions): TrainingCourseMasterFactoryInterface<{}, TrainingCourseMasterTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingCourseMasterTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingCourseMasterFactoryDefineOptions<TTransients>>(options?: TOptions) => TrainingCourseMasterFactoryInterface<TTransients, TrainingCourseMasterTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingCourseMaster} model.
 *
 * @param options
 * @returns factory {@link TrainingCourseMasterFactoryInterface}
 */
export declare const defineTrainingCourseMasterFactory: TrainingCourseMasterFactoryBuilder;
type TrainingCoursetrainingCourseMasterFactory = {
    _factoryFor: "TrainingCourseMaster";
    build: () => PromiseLike<Prisma.TrainingCourseMasterCreateNestedOneWithoutTrainingCoursesInput["create"]>;
};
type TrainingCourseFactoryDefineInput = {
    courseId?: string;
    courseInternalId?: (bigint | number);
    courseName?: string;
    distance?: number | null;
    lastNFurlong?: number | null;
    openedAt?: Date | null;
    closedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    tempFacilityId?: (bigint | number) | null;
    trainingCourseMaster?: TrainingCoursetrainingCourseMasterFactory | Prisma.TrainingCourseMasterCreateNestedOneWithoutTrainingCoursesInput;
    reportSectionWorkoutConditions?: Prisma.ReportSectionWorkoutConditionCreateNestedManyWithoutTrainingCourseInput;
    horseTrainingRecords?: Prisma.HorseTrainingRecordCreateNestedManyWithoutTrainingCourseInput;
    HorseCoursePitchAverage?: Prisma.HorseCoursePitchAverageCreateNestedManyWithoutCourseInput;
    TrainingPeriod?: Prisma.TrainingPeriodCreateNestedManyWithoutTrainingCourseInput;
};
type TrainingCourseTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingCourseFactoryDefineInput, never>>;
type TrainingCourseFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingCourseFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingCourse, Prisma.TrainingCourseCreateInput, TTransients>;
type TrainingCourseFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<TrainingCourseFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: TrainingCourseFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingCourse, Prisma.TrainingCourseCreateInput, TTransients>;
type TrainingCourseTraitKeys<TOptions extends TrainingCourseFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingCourseFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingCourse";
    build(inputData?: Partial<Prisma.TrainingCourseCreateInput & TTransients>): PromiseLike<Prisma.TrainingCourseCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingCourseCreateInput & TTransients>): PromiseLike<Prisma.TrainingCourseCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingCourseCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingCourseCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingCourseCreateInput & TTransients>): PromiseLike<Prisma.TrainingCourseCreateInput[]>;
    pickForConnect(inputData: TrainingCourse): Pick<TrainingCourse, "courseId">;
    create(inputData?: Partial<Prisma.TrainingCourseCreateInput & TTransients>): PromiseLike<TrainingCourse>;
    createList(list: readonly Partial<Prisma.TrainingCourseCreateInput & TTransients>[]): PromiseLike<TrainingCourse[]>;
    createList(count: number, item?: Partial<Prisma.TrainingCourseCreateInput & TTransients>): PromiseLike<TrainingCourse[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingCourseCreateInput & TTransients>): PromiseLike<Pick<TrainingCourse, "courseId">>;
}
export interface TrainingCourseFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingCourseFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingCourseFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingCourseFactoryBuilder {
    <TOptions extends TrainingCourseFactoryDefineOptions>(options?: TOptions): TrainingCourseFactoryInterface<{}, TrainingCourseTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingCourseTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingCourseFactoryDefineOptions<TTransients>>(options?: TOptions) => TrainingCourseFactoryInterface<TTransients, TrainingCourseTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingCourse} model.
 *
 * @param options
 * @returns factory {@link TrainingCourseFactoryInterface}
 */
export declare const defineTrainingCourseFactory: TrainingCourseFactoryBuilder;
type FurlongLineFactoryDefineInput = {
    furlongLineInternalId?: (bigint | number);
    facilityId?: (bigint | number);
    courseId?: string | null;
    furlong?: number;
    direction?: FurlongLineDirection;
    line?: Buffer;
    createdAt?: Date;
    updatedAt?: Date;
};
type FurlongLineTransientFields = Record<string, unknown> & Partial<Record<keyof FurlongLineFactoryDefineInput, never>>;
type FurlongLineFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<FurlongLineFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<FurlongLine, Prisma.FurlongLineCreateInput, TTransients>;
type FurlongLineFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<FurlongLineFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: FurlongLineFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<FurlongLine, Prisma.FurlongLineCreateInput, TTransients>;
type FurlongLineTraitKeys<TOptions extends FurlongLineFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface FurlongLineFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "FurlongLine";
    build(inputData?: Partial<Prisma.FurlongLineCreateInput & TTransients>): PromiseLike<Prisma.FurlongLineCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.FurlongLineCreateInput & TTransients>): PromiseLike<Prisma.FurlongLineCreateInput>;
    buildList(list: readonly Partial<Prisma.FurlongLineCreateInput & TTransients>[]): PromiseLike<Prisma.FurlongLineCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.FurlongLineCreateInput & TTransients>): PromiseLike<Prisma.FurlongLineCreateInput[]>;
    pickForConnect(inputData: FurlongLine): Pick<FurlongLine, "furlongLineInternalId">;
    create(inputData?: Partial<Prisma.FurlongLineCreateInput & TTransients>): PromiseLike<FurlongLine>;
    createList(list: readonly Partial<Prisma.FurlongLineCreateInput & TTransients>[]): PromiseLike<FurlongLine[]>;
    createList(count: number, item?: Partial<Prisma.FurlongLineCreateInput & TTransients>): PromiseLike<FurlongLine[]>;
    createForConnect(inputData?: Partial<Prisma.FurlongLineCreateInput & TTransients>): PromiseLike<Pick<FurlongLine, "furlongLineInternalId">>;
}
export interface FurlongLineFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends FurlongLineFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): FurlongLineFactoryInterfaceWithoutTraits<TTransients>;
}
interface FurlongLineFactoryBuilder {
    <TOptions extends FurlongLineFactoryDefineOptions>(options?: TOptions): FurlongLineFactoryInterface<{}, FurlongLineTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends FurlongLineTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends FurlongLineFactoryDefineOptions<TTransients>>(options?: TOptions) => FurlongLineFactoryInterface<TTransients, FurlongLineTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link FurlongLine} model.
 *
 * @param options
 * @returns factory {@link FurlongLineFactoryInterface}
 */
export declare const defineFurlongLineFactory: FurlongLineFactoryBuilder;
type HorsestableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutHorsesInput["create"]>;
};
type HorseorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutHorsesInput["create"]>;
};
type HorsemasterHorseFactory = {
    _factoryFor: "MasterHorse";
    build: () => PromiseLike<Prisma.MasterHorseCreateNestedOneWithoutHorsesInput["create"]>;
};
type HorsehorseStatusFactory = {
    _factoryFor: "HorseStatus";
    build: () => PromiseLike<Prisma.HorseStatusCreateNestedOneWithoutHorseInput["create"]>;
};
type HorseFactoryDefineInput = {
    horseId?: (bigint | number);
    name?: string;
    gender?: Gender | null;
    birthYear?: number | null;
    fatherId?: number | null;
    motherId?: number | null;
    rfId?: number | null;
    profilePicPath?: string | null;
    manageStatus?: string;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    stable: HorsestableFactory | Prisma.StableCreateNestedOneWithoutHorsesInput;
    organization?: HorseorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutHorsesInput;
    masterHorse?: HorsemasterHorseFactory | Prisma.MasterHorseCreateNestedOneWithoutHorsesInput;
    horseStableHistories?: Prisma.HorseStableHistoryCreateNestedManyWithoutHorseInput;
    organizationOwnerHorseRelations?: Prisma.OrganizationOwnerHorseRelationCreateNestedManyWithoutHorseInput;
    reports?: Prisma.ReportCreateNestedManyWithoutHorseInput;
    pendingSendReports?: Prisma.PendingSendReportCreateNestedManyWithoutHorseInput;
    horseDailyRecords?: Prisma.HorseDailyRecordCreateNestedManyWithoutHorseInput;
    horseHandoverNotes?: Prisma.HorseHandoverNoteCreateNestedManyWithoutHorseInput;
    trainingPartners?: Prisma.TrainingPartnerCreateNestedManyWithoutHorseInput;
    ReportGenerateRequest?: Prisma.ReportGenerateRequestCreateNestedManyWithoutHorseInput;
    trainings?: Prisma.TrainingCreateNestedManyWithoutHorseInput;
    businessTripHistories?: Prisma.BusinessTripHistoryHorseCreateNestedManyWithoutHorseInput;
    StableTmTransportRecord?: Prisma.StableTmTransportRecordCreateNestedManyWithoutHorseInput;
    horseStatus?: HorsehorseStatusFactory | Prisma.HorseStatusCreateNestedOneWithoutHorseInput;
};
type HorseTransientFields = Record<string, unknown> & Partial<Record<keyof HorseFactoryDefineInput, never>>;
type HorseFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Horse, Prisma.HorseCreateInput, TTransients>;
type HorseFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Horse, Prisma.HorseCreateInput, TTransients>;
type HorseTraitKeys<TOptions extends HorseFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Horse";
    build(inputData?: Partial<Prisma.HorseCreateInput & TTransients>): PromiseLike<Prisma.HorseCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseCreateInput & TTransients>): PromiseLike<Prisma.HorseCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseCreateInput & TTransients>[]): PromiseLike<Prisma.HorseCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseCreateInput & TTransients>): PromiseLike<Prisma.HorseCreateInput[]>;
    pickForConnect(inputData: Horse): Pick<Horse, "horseId">;
    create(inputData?: Partial<Prisma.HorseCreateInput & TTransients>): PromiseLike<Horse>;
    createList(list: readonly Partial<Prisma.HorseCreateInput & TTransients>[]): PromiseLike<Horse[]>;
    createList(count: number, item?: Partial<Prisma.HorseCreateInput & TTransients>): PromiseLike<Horse[]>;
    createForConnect(inputData?: Partial<Prisma.HorseCreateInput & TTransients>): PromiseLike<Pick<Horse, "horseId">>;
}
export interface HorseFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseFactoryBuilder {
    <TOptions extends HorseFactoryDefineOptions>(options: TOptions): HorseFactoryInterface<{}, HorseTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseFactoryDefineOptions<TTransients>>(options: TOptions) => HorseFactoryInterface<TTransients, HorseTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Horse} model.
 *
 * @param options
 * @returns factory {@link HorseFactoryInterface}
 */
export declare const defineHorseFactory: HorseFactoryBuilder;
type HorseStableHistoryhorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutHorseStableHistoriesInput["create"]>;
};
type HorseStableHistorystableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutHorseStableHistoriesInput["create"]>;
};
type HorseStableHistoryFactoryDefineInput = {
    horseStableHistoryUuid?: Buffer;
    inStable?: boolean;
    createdAt?: Date;
    horse: HorseStableHistoryhorseFactory | Prisma.HorseCreateNestedOneWithoutHorseStableHistoriesInput;
    stable: HorseStableHistorystableFactory | Prisma.StableCreateNestedOneWithoutHorseStableHistoriesInput;
};
type HorseStableHistoryTransientFields = Record<string, unknown> & Partial<Record<keyof HorseStableHistoryFactoryDefineInput, never>>;
type HorseStableHistoryFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseStableHistoryFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseStableHistory, Prisma.HorseStableHistoryCreateInput, TTransients>;
type HorseStableHistoryFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseStableHistoryFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseStableHistoryFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseStableHistory, Prisma.HorseStableHistoryCreateInput, TTransients>;
type HorseStableHistoryTraitKeys<TOptions extends HorseStableHistoryFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseStableHistoryFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseStableHistory";
    build(inputData?: Partial<Prisma.HorseStableHistoryCreateInput & TTransients>): PromiseLike<Prisma.HorseStableHistoryCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseStableHistoryCreateInput & TTransients>): PromiseLike<Prisma.HorseStableHistoryCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseStableHistoryCreateInput & TTransients>[]): PromiseLike<Prisma.HorseStableHistoryCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseStableHistoryCreateInput & TTransients>): PromiseLike<Prisma.HorseStableHistoryCreateInput[]>;
    pickForConnect(inputData: HorseStableHistory): Pick<HorseStableHistory, "horseStableHistoryUuid">;
    create(inputData?: Partial<Prisma.HorseStableHistoryCreateInput & TTransients>): PromiseLike<HorseStableHistory>;
    createList(list: readonly Partial<Prisma.HorseStableHistoryCreateInput & TTransients>[]): PromiseLike<HorseStableHistory[]>;
    createList(count: number, item?: Partial<Prisma.HorseStableHistoryCreateInput & TTransients>): PromiseLike<HorseStableHistory[]>;
    createForConnect(inputData?: Partial<Prisma.HorseStableHistoryCreateInput & TTransients>): PromiseLike<Pick<HorseStableHistory, "horseStableHistoryUuid">>;
}
export interface HorseStableHistoryFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseStableHistoryFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseStableHistoryFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseStableHistoryFactoryBuilder {
    <TOptions extends HorseStableHistoryFactoryDefineOptions>(options: TOptions): HorseStableHistoryFactoryInterface<{}, HorseStableHistoryTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseStableHistoryTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseStableHistoryFactoryDefineOptions<TTransients>>(options: TOptions) => HorseStableHistoryFactoryInterface<TTransients, HorseStableHistoryTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseStableHistory} model.
 *
 * @param options
 * @returns factory {@link HorseStableHistoryFactoryInterface}
 */
export declare const defineHorseStableHistoryFactory: HorseStableHistoryFactoryBuilder;
type HorseStatushorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutHorseStatusInput["create"]>;
};
type HorseStatusoutsideFarmFactory = {
    _factoryFor: "StableTmOutsideFarm";
    build: () => PromiseLike<Prisma.StableTmOutsideFarmCreateNestedOneWithoutHorseStatusInput["create"]>;
};
type HorseStatusFactoryDefineInput = {
    horseStatusId?: Buffer;
    horseStatusInternalId?: (bigint | number);
    inStable?: boolean;
    latestStableInDate?: Date | null;
    latestStableOutDate?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    horse: HorseStatushorseFactory | Prisma.HorseCreateNestedOneWithoutHorseStatusInput;
    outsideFarm?: HorseStatusoutsideFarmFactory | Prisma.StableTmOutsideFarmCreateNestedOneWithoutHorseStatusInput;
};
type HorseStatusTransientFields = Record<string, unknown> & Partial<Record<keyof HorseStatusFactoryDefineInput, never>>;
type HorseStatusFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseStatusFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseStatus, Prisma.HorseStatusCreateInput, TTransients>;
type HorseStatusFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseStatusFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseStatusFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseStatus, Prisma.HorseStatusCreateInput, TTransients>;
type HorseStatusTraitKeys<TOptions extends HorseStatusFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseStatusFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseStatus";
    build(inputData?: Partial<Prisma.HorseStatusCreateInput & TTransients>): PromiseLike<Prisma.HorseStatusCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseStatusCreateInput & TTransients>): PromiseLike<Prisma.HorseStatusCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseStatusCreateInput & TTransients>[]): PromiseLike<Prisma.HorseStatusCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseStatusCreateInput & TTransients>): PromiseLike<Prisma.HorseStatusCreateInput[]>;
    pickForConnect(inputData: HorseStatus): Pick<HorseStatus, "horseStatusId">;
    create(inputData?: Partial<Prisma.HorseStatusCreateInput & TTransients>): PromiseLike<HorseStatus>;
    createList(list: readonly Partial<Prisma.HorseStatusCreateInput & TTransients>[]): PromiseLike<HorseStatus[]>;
    createList(count: number, item?: Partial<Prisma.HorseStatusCreateInput & TTransients>): PromiseLike<HorseStatus[]>;
    createForConnect(inputData?: Partial<Prisma.HorseStatusCreateInput & TTransients>): PromiseLike<Pick<HorseStatus, "horseStatusId">>;
}
export interface HorseStatusFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseStatusFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseStatusFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseStatusFactoryBuilder {
    <TOptions extends HorseStatusFactoryDefineOptions>(options: TOptions): HorseStatusFactoryInterface<{}, HorseStatusTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseStatusTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseStatusFactoryDefineOptions<TTransients>>(options: TOptions) => HorseStatusFactoryInterface<TTransients, HorseStatusTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseStatus} model.
 *
 * @param options
 * @returns factory {@link HorseStatusFactoryInterface}
 */
export declare const defineHorseStatusFactory: HorseStatusFactoryBuilder;
type MasterHorseFactoryDefineInput = {
    masterHorseId?: string;
    horseName?: string;
    horseNameEn?: string | null;
    motherName?: string;
    gender?: string;
    birthYear?: number;
    stableName?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horses?: Prisma.HorseCreateNestedManyWithoutMasterHorseInput;
    ownerHorses?: Prisma.OwnerHorseCreateNestedManyWithoutMasterHorseInput;
    HorseCoursePitchAverage?: Prisma.HorseCoursePitchAverageCreateNestedManyWithoutMasterHorseInput;
};
type MasterHorseTransientFields = Record<string, unknown> & Partial<Record<keyof MasterHorseFactoryDefineInput, never>>;
type MasterHorseFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<MasterHorseFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<MasterHorse, Prisma.MasterHorseCreateInput, TTransients>;
type MasterHorseFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<MasterHorseFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: MasterHorseFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<MasterHorse, Prisma.MasterHorseCreateInput, TTransients>;
type MasterHorseTraitKeys<TOptions extends MasterHorseFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface MasterHorseFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "MasterHorse";
    build(inputData?: Partial<Prisma.MasterHorseCreateInput & TTransients>): PromiseLike<Prisma.MasterHorseCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.MasterHorseCreateInput & TTransients>): PromiseLike<Prisma.MasterHorseCreateInput>;
    buildList(list: readonly Partial<Prisma.MasterHorseCreateInput & TTransients>[]): PromiseLike<Prisma.MasterHorseCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.MasterHorseCreateInput & TTransients>): PromiseLike<Prisma.MasterHorseCreateInput[]>;
    pickForConnect(inputData: MasterHorse): Pick<MasterHorse, "masterHorseId">;
    create(inputData?: Partial<Prisma.MasterHorseCreateInput & TTransients>): PromiseLike<MasterHorse>;
    createList(list: readonly Partial<Prisma.MasterHorseCreateInput & TTransients>[]): PromiseLike<MasterHorse[]>;
    createList(count: number, item?: Partial<Prisma.MasterHorseCreateInput & TTransients>): PromiseLike<MasterHorse[]>;
    createForConnect(inputData?: Partial<Prisma.MasterHorseCreateInput & TTransients>): PromiseLike<Pick<MasterHorse, "masterHorseId">>;
}
export interface MasterHorseFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends MasterHorseFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): MasterHorseFactoryInterfaceWithoutTraits<TTransients>;
}
interface MasterHorseFactoryBuilder {
    <TOptions extends MasterHorseFactoryDefineOptions>(options?: TOptions): MasterHorseFactoryInterface<{}, MasterHorseTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends MasterHorseTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends MasterHorseFactoryDefineOptions<TTransients>>(options?: TOptions) => MasterHorseFactoryInterface<TTransients, MasterHorseTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link MasterHorse} model.
 *
 * @param options
 * @returns factory {@link MasterHorseFactoryInterface}
 */
export declare const defineMasterHorseFactory: MasterHorseFactoryBuilder;
type HorseHandoverNotehorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutHorseHandoverNotesInput["create"]>;
};
type HorseHandoverNoteFactoryDefineInput = {
    horseHandoverNoteId?: Buffer;
    horseHandoverNoteInternalId?: (bigint | number);
    handoverNote?: string | null;
    nextRaceEquipmentNote?: string | null;
    fodderNote?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horse: HorseHandoverNotehorseFactory | Prisma.HorseCreateNestedOneWithoutHorseHandoverNotesInput;
};
type HorseHandoverNoteTransientFields = Record<string, unknown> & Partial<Record<keyof HorseHandoverNoteFactoryDefineInput, never>>;
type HorseHandoverNoteFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseHandoverNoteFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseHandoverNote, Prisma.HorseHandoverNoteCreateInput, TTransients>;
type HorseHandoverNoteFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseHandoverNoteFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseHandoverNoteFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseHandoverNote, Prisma.HorseHandoverNoteCreateInput, TTransients>;
type HorseHandoverNoteTraitKeys<TOptions extends HorseHandoverNoteFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseHandoverNoteFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseHandoverNote";
    build(inputData?: Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>): PromiseLike<Prisma.HorseHandoverNoteCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>): PromiseLike<Prisma.HorseHandoverNoteCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>[]): PromiseLike<Prisma.HorseHandoverNoteCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>): PromiseLike<Prisma.HorseHandoverNoteCreateInput[]>;
    pickForConnect(inputData: HorseHandoverNote): Pick<HorseHandoverNote, "horseHandoverNoteId">;
    create(inputData?: Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>): PromiseLike<HorseHandoverNote>;
    createList(list: readonly Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>[]): PromiseLike<HorseHandoverNote[]>;
    createList(count: number, item?: Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>): PromiseLike<HorseHandoverNote[]>;
    createForConnect(inputData?: Partial<Prisma.HorseHandoverNoteCreateInput & TTransients>): PromiseLike<Pick<HorseHandoverNote, "horseHandoverNoteId">>;
}
export interface HorseHandoverNoteFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseHandoverNoteFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseHandoverNoteFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseHandoverNoteFactoryBuilder {
    <TOptions extends HorseHandoverNoteFactoryDefineOptions>(options: TOptions): HorseHandoverNoteFactoryInterface<{}, HorseHandoverNoteTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseHandoverNoteTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseHandoverNoteFactoryDefineOptions<TTransients>>(options: TOptions) => HorseHandoverNoteFactoryInterface<TTransients, HorseHandoverNoteTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseHandoverNote} model.
 *
 * @param options
 * @returns factory {@link HorseHandoverNoteFactoryInterface}
 */
export declare const defineHorseHandoverNoteFactory: HorseHandoverNoteFactoryBuilder;
type EmailLogFactoryDefineInput = {
    emailLogInternalId?: (bigint | number);
    email?: string;
    recipientFirebaseUid?: string | null;
    senderFirebaseUid?: string | null;
    emailTemplateKey?: string;
    createdAt?: Date;
};
type EmailLogTransientFields = Record<string, unknown> & Partial<Record<keyof EmailLogFactoryDefineInput, never>>;
type EmailLogFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<EmailLogFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<EmailLog, Prisma.EmailLogCreateInput, TTransients>;
type EmailLogFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<EmailLogFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: EmailLogFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<EmailLog, Prisma.EmailLogCreateInput, TTransients>;
type EmailLogTraitKeys<TOptions extends EmailLogFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface EmailLogFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "EmailLog";
    build(inputData?: Partial<Prisma.EmailLogCreateInput & TTransients>): PromiseLike<Prisma.EmailLogCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.EmailLogCreateInput & TTransients>): PromiseLike<Prisma.EmailLogCreateInput>;
    buildList(list: readonly Partial<Prisma.EmailLogCreateInput & TTransients>[]): PromiseLike<Prisma.EmailLogCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.EmailLogCreateInput & TTransients>): PromiseLike<Prisma.EmailLogCreateInput[]>;
    pickForConnect(inputData: EmailLog): Pick<EmailLog, "emailLogInternalId">;
    create(inputData?: Partial<Prisma.EmailLogCreateInput & TTransients>): PromiseLike<EmailLog>;
    createList(list: readonly Partial<Prisma.EmailLogCreateInput & TTransients>[]): PromiseLike<EmailLog[]>;
    createList(count: number, item?: Partial<Prisma.EmailLogCreateInput & TTransients>): PromiseLike<EmailLog[]>;
    createForConnect(inputData?: Partial<Prisma.EmailLogCreateInput & TTransients>): PromiseLike<Pick<EmailLog, "emailLogInternalId">>;
}
export interface EmailLogFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends EmailLogFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): EmailLogFactoryInterfaceWithoutTraits<TTransients>;
}
interface EmailLogFactoryBuilder {
    <TOptions extends EmailLogFactoryDefineOptions>(options?: TOptions): EmailLogFactoryInterface<{}, EmailLogTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends EmailLogTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends EmailLogFactoryDefineOptions<TTransients>>(options?: TOptions) => EmailLogFactoryInterface<TTransients, EmailLogTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link EmailLog} model.
 *
 * @param options
 * @returns factory {@link EmailLogFactoryInterface}
 */
export declare const defineEmailLogFactory: EmailLogFactoryBuilder;
type AiGenerateLogFactoryDefineInput = {
    aiGenerateLogId?: string;
    aiGenerateLogInternalId?: (bigint | number);
    model?: string | null;
    requestPrompt?: string | null;
    response?: string | null;
    promptTokens?: number | null;
    completionTokens?: number | null;
    totalTokens?: number | null;
    finishReason?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportGenerateRequests?: Prisma.ReportGenerateRequestCreateNestedManyWithoutAiGenerateLogInput;
};
type AiGenerateLogTransientFields = Record<string, unknown> & Partial<Record<keyof AiGenerateLogFactoryDefineInput, never>>;
type AiGenerateLogFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<AiGenerateLogFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<AiGenerateLog, Prisma.AiGenerateLogCreateInput, TTransients>;
type AiGenerateLogFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<AiGenerateLogFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: AiGenerateLogFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<AiGenerateLog, Prisma.AiGenerateLogCreateInput, TTransients>;
type AiGenerateLogTraitKeys<TOptions extends AiGenerateLogFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface AiGenerateLogFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "AiGenerateLog";
    build(inputData?: Partial<Prisma.AiGenerateLogCreateInput & TTransients>): PromiseLike<Prisma.AiGenerateLogCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.AiGenerateLogCreateInput & TTransients>): PromiseLike<Prisma.AiGenerateLogCreateInput>;
    buildList(list: readonly Partial<Prisma.AiGenerateLogCreateInput & TTransients>[]): PromiseLike<Prisma.AiGenerateLogCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.AiGenerateLogCreateInput & TTransients>): PromiseLike<Prisma.AiGenerateLogCreateInput[]>;
    pickForConnect(inputData: AiGenerateLog): Pick<AiGenerateLog, "aiGenerateLogId">;
    create(inputData?: Partial<Prisma.AiGenerateLogCreateInput & TTransients>): PromiseLike<AiGenerateLog>;
    createList(list: readonly Partial<Prisma.AiGenerateLogCreateInput & TTransients>[]): PromiseLike<AiGenerateLog[]>;
    createList(count: number, item?: Partial<Prisma.AiGenerateLogCreateInput & TTransients>): PromiseLike<AiGenerateLog[]>;
    createForConnect(inputData?: Partial<Prisma.AiGenerateLogCreateInput & TTransients>): PromiseLike<Pick<AiGenerateLog, "aiGenerateLogId">>;
}
export interface AiGenerateLogFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends AiGenerateLogFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): AiGenerateLogFactoryInterfaceWithoutTraits<TTransients>;
}
interface AiGenerateLogFactoryBuilder {
    <TOptions extends AiGenerateLogFactoryDefineOptions>(options?: TOptions): AiGenerateLogFactoryInterface<{}, AiGenerateLogTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends AiGenerateLogTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends AiGenerateLogFactoryDefineOptions<TTransients>>(options?: TOptions) => AiGenerateLogFactoryInterface<TTransients, AiGenerateLogTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link AiGenerateLog} model.
 *
 * @param options
 * @returns factory {@link AiGenerateLogFactoryInterface}
 */
export declare const defineAiGenerateLogFactory: AiGenerateLogFactoryBuilder;
type OrganizationOwnerorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutOrganizationOwnersInput["create"]>;
};
type OrganizationOwnerownerFactory = {
    _factoryFor: "Owner";
    build: () => PromiseLike<Prisma.OwnerCreateNestedOneWithoutOrganizationOwnersInput["create"]>;
};
type OrganizationOwnerFactoryDefineInput = {
    organizationOwnerId?: string;
    organizationOwnerInternalId?: (bigint | number);
    organizationOwnerName?: string;
    createdAt?: Date;
    updatedAt?: Date;
    organization: OrganizationOwnerorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutOrganizationOwnersInput;
    owner: OrganizationOwnerownerFactory | Prisma.OwnerCreateNestedOneWithoutOrganizationOwnersInput;
    invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationOwnerInput;
    organizationOwnerHorseRelations?: Prisma.OrganizationOwnerHorseRelationCreateNestedManyWithoutOrganizationOwnerInput;
    sentReports?: Prisma.SentReportCreateNestedManyWithoutOrganizationOwnerInput;
    pendingSendReports?: Prisma.PendingSendReportCreateNestedManyWithoutOrganizationOwnerInput;
};
type OrganizationOwnerTransientFields = Record<string, unknown> & Partial<Record<keyof OrganizationOwnerFactoryDefineInput, never>>;
type OrganizationOwnerFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OrganizationOwnerFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<OrganizationOwner, Prisma.OrganizationOwnerCreateInput, TTransients>;
type OrganizationOwnerFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<OrganizationOwnerFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: OrganizationOwnerFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<OrganizationOwner, Prisma.OrganizationOwnerCreateInput, TTransients>;
type OrganizationOwnerTraitKeys<TOptions extends OrganizationOwnerFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OrganizationOwnerFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "OrganizationOwner";
    build(inputData?: Partial<Prisma.OrganizationOwnerCreateInput & TTransients>): PromiseLike<Prisma.OrganizationOwnerCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OrganizationOwnerCreateInput & TTransients>): PromiseLike<Prisma.OrganizationOwnerCreateInput>;
    buildList(list: readonly Partial<Prisma.OrganizationOwnerCreateInput & TTransients>[]): PromiseLike<Prisma.OrganizationOwnerCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OrganizationOwnerCreateInput & TTransients>): PromiseLike<Prisma.OrganizationOwnerCreateInput[]>;
    pickForConnect(inputData: OrganizationOwner): Pick<OrganizationOwner, "organizationOwnerId">;
    create(inputData?: Partial<Prisma.OrganizationOwnerCreateInput & TTransients>): PromiseLike<OrganizationOwner>;
    createList(list: readonly Partial<Prisma.OrganizationOwnerCreateInput & TTransients>[]): PromiseLike<OrganizationOwner[]>;
    createList(count: number, item?: Partial<Prisma.OrganizationOwnerCreateInput & TTransients>): PromiseLike<OrganizationOwner[]>;
    createForConnect(inputData?: Partial<Prisma.OrganizationOwnerCreateInput & TTransients>): PromiseLike<Pick<OrganizationOwner, "organizationOwnerId">>;
}
export interface OrganizationOwnerFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OrganizationOwnerFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OrganizationOwnerFactoryInterfaceWithoutTraits<TTransients>;
}
interface OrganizationOwnerFactoryBuilder {
    <TOptions extends OrganizationOwnerFactoryDefineOptions>(options: TOptions): OrganizationOwnerFactoryInterface<{}, OrganizationOwnerTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OrganizationOwnerTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OrganizationOwnerFactoryDefineOptions<TTransients>>(options: TOptions) => OrganizationOwnerFactoryInterface<TTransients, OrganizationOwnerTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link OrganizationOwner} model.
 *
 * @param options
 * @returns factory {@link OrganizationOwnerFactoryInterface}
 */
export declare const defineOrganizationOwnerFactory: OrganizationOwnerFactoryBuilder;
type OrganizationOwnerHorseRelationorganizationOwnerFactory = {
    _factoryFor: "OrganizationOwner";
    build: () => PromiseLike<Prisma.OrganizationOwnerCreateNestedOneWithoutOrganizationOwnerHorseRelationsInput["create"]>;
};
type OrganizationOwnerHorseRelationhorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutOrganizationOwnerHorseRelationsInput["create"]>;
};
type OrganizationOwnerHorseRelationFactoryDefineInput = {
    organizationOwnerHorseRelationId?: string;
    organizationOwnerHorseRelationInternalId?: (bigint | number);
    createdAt?: Date;
    updatedAt?: Date;
    organizationOwner: OrganizationOwnerHorseRelationorganizationOwnerFactory | Prisma.OrganizationOwnerCreateNestedOneWithoutOrganizationOwnerHorseRelationsInput;
    horse: OrganizationOwnerHorseRelationhorseFactory | Prisma.HorseCreateNestedOneWithoutOrganizationOwnerHorseRelationsInput;
};
type OrganizationOwnerHorseRelationTransientFields = Record<string, unknown> & Partial<Record<keyof OrganizationOwnerHorseRelationFactoryDefineInput, never>>;
type OrganizationOwnerHorseRelationFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OrganizationOwnerHorseRelationFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<OrganizationOwnerHorseRelation, Prisma.OrganizationOwnerHorseRelationCreateInput, TTransients>;
type OrganizationOwnerHorseRelationFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<OrganizationOwnerHorseRelationFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: OrganizationOwnerHorseRelationFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<OrganizationOwnerHorseRelation, Prisma.OrganizationOwnerHorseRelationCreateInput, TTransients>;
type OrganizationOwnerHorseRelationTraitKeys<TOptions extends OrganizationOwnerHorseRelationFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OrganizationOwnerHorseRelationFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "OrganizationOwnerHorseRelation";
    build(inputData?: Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>): PromiseLike<Prisma.OrganizationOwnerHorseRelationCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>): PromiseLike<Prisma.OrganizationOwnerHorseRelationCreateInput>;
    buildList(list: readonly Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>[]): PromiseLike<Prisma.OrganizationOwnerHorseRelationCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>): PromiseLike<Prisma.OrganizationOwnerHorseRelationCreateInput[]>;
    pickForConnect(inputData: OrganizationOwnerHorseRelation): Pick<OrganizationOwnerHorseRelation, "organizationOwnerHorseRelationId">;
    create(inputData?: Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>): PromiseLike<OrganizationOwnerHorseRelation>;
    createList(list: readonly Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>[]): PromiseLike<OrganizationOwnerHorseRelation[]>;
    createList(count: number, item?: Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>): PromiseLike<OrganizationOwnerHorseRelation[]>;
    createForConnect(inputData?: Partial<Prisma.OrganizationOwnerHorseRelationCreateInput & TTransients>): PromiseLike<Pick<OrganizationOwnerHorseRelation, "organizationOwnerHorseRelationId">>;
}
export interface OrganizationOwnerHorseRelationFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OrganizationOwnerHorseRelationFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OrganizationOwnerHorseRelationFactoryInterfaceWithoutTraits<TTransients>;
}
interface OrganizationOwnerHorseRelationFactoryBuilder {
    <TOptions extends OrganizationOwnerHorseRelationFactoryDefineOptions>(options: TOptions): OrganizationOwnerHorseRelationFactoryInterface<{}, OrganizationOwnerHorseRelationTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OrganizationOwnerHorseRelationTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OrganizationOwnerHorseRelationFactoryDefineOptions<TTransients>>(options: TOptions) => OrganizationOwnerHorseRelationFactoryInterface<TTransients, OrganizationOwnerHorseRelationTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link OrganizationOwnerHorseRelation} model.
 *
 * @param options
 * @returns factory {@link OrganizationOwnerHorseRelationFactoryInterface}
 */
export declare const defineOrganizationOwnerHorseRelationFactory: OrganizationOwnerHorseRelationFactoryBuilder;
type InvitationorganizationOwnerFactory = {
    _factoryFor: "OrganizationOwner";
    build: () => PromiseLike<Prisma.OrganizationOwnerCreateNestedOneWithoutInvitationsInput["create"]>;
};
type InvitationFactoryDefineInput = {
    invitationId?: string;
    invitationInternalId?: (bigint | number);
    token?: string;
    acceptedAt?: Date | null;
    method?: InvitationMethod;
    inviteEmail?: string | null;
    email_dispatch_count?: number;
    createdAt?: Date;
    updatedAt?: Date;
    organizationOwner: InvitationorganizationOwnerFactory | Prisma.OrganizationOwnerCreateNestedOneWithoutInvitationsInput;
};
type InvitationTransientFields = Record<string, unknown> & Partial<Record<keyof InvitationFactoryDefineInput, never>>;
type InvitationFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<InvitationFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Invitation, Prisma.InvitationCreateInput, TTransients>;
type InvitationFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<InvitationFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: InvitationFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Invitation, Prisma.InvitationCreateInput, TTransients>;
type InvitationTraitKeys<TOptions extends InvitationFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface InvitationFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Invitation";
    build(inputData?: Partial<Prisma.InvitationCreateInput & TTransients>): PromiseLike<Prisma.InvitationCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.InvitationCreateInput & TTransients>): PromiseLike<Prisma.InvitationCreateInput>;
    buildList(list: readonly Partial<Prisma.InvitationCreateInput & TTransients>[]): PromiseLike<Prisma.InvitationCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.InvitationCreateInput & TTransients>): PromiseLike<Prisma.InvitationCreateInput[]>;
    pickForConnect(inputData: Invitation): Pick<Invitation, "invitationId">;
    create(inputData?: Partial<Prisma.InvitationCreateInput & TTransients>): PromiseLike<Invitation>;
    createList(list: readonly Partial<Prisma.InvitationCreateInput & TTransients>[]): PromiseLike<Invitation[]>;
    createList(count: number, item?: Partial<Prisma.InvitationCreateInput & TTransients>): PromiseLike<Invitation[]>;
    createForConnect(inputData?: Partial<Prisma.InvitationCreateInput & TTransients>): PromiseLike<Pick<Invitation, "invitationId">>;
}
export interface InvitationFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends InvitationFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): InvitationFactoryInterfaceWithoutTraits<TTransients>;
}
interface InvitationFactoryBuilder {
    <TOptions extends InvitationFactoryDefineOptions>(options: TOptions): InvitationFactoryInterface<{}, InvitationTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends InvitationTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends InvitationFactoryDefineOptions<TTransients>>(options: TOptions) => InvitationFactoryInterface<TTransients, InvitationTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Invitation} model.
 *
 * @param options
 * @returns factory {@link InvitationFactoryInterface}
 */
export declare const defineInvitationFactory: InvitationFactoryBuilder;
type OwnerFactoryDefineInput = {
    ownerId?: string;
    ownerInternalId?: (bigint | number);
    ownerName?: string | null;
    firebaseUid?: string | null;
    verifiedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    organizationOwners?: Prisma.OrganizationOwnerCreateNestedManyWithoutOwnerInput;
    ownerHorses?: Prisma.OwnerHorseCreateNestedManyWithoutOwnerInput;
    userTermsAcceptances?: Prisma.UserTermsAcceptancesCreateNestedManyWithoutOwnerInput;
};
type OwnerTransientFields = Record<string, unknown> & Partial<Record<keyof OwnerFactoryDefineInput, never>>;
type OwnerFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OwnerFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Owner, Prisma.OwnerCreateInput, TTransients>;
type OwnerFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<OwnerFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: OwnerFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Owner, Prisma.OwnerCreateInput, TTransients>;
type OwnerTraitKeys<TOptions extends OwnerFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OwnerFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Owner";
    build(inputData?: Partial<Prisma.OwnerCreateInput & TTransients>): PromiseLike<Prisma.OwnerCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OwnerCreateInput & TTransients>): PromiseLike<Prisma.OwnerCreateInput>;
    buildList(list: readonly Partial<Prisma.OwnerCreateInput & TTransients>[]): PromiseLike<Prisma.OwnerCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OwnerCreateInput & TTransients>): PromiseLike<Prisma.OwnerCreateInput[]>;
    pickForConnect(inputData: Owner): Pick<Owner, "ownerId">;
    create(inputData?: Partial<Prisma.OwnerCreateInput & TTransients>): PromiseLike<Owner>;
    createList(list: readonly Partial<Prisma.OwnerCreateInput & TTransients>[]): PromiseLike<Owner[]>;
    createList(count: number, item?: Partial<Prisma.OwnerCreateInput & TTransients>): PromiseLike<Owner[]>;
    createForConnect(inputData?: Partial<Prisma.OwnerCreateInput & TTransients>): PromiseLike<Pick<Owner, "ownerId">>;
}
export interface OwnerFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OwnerFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OwnerFactoryInterfaceWithoutTraits<TTransients>;
}
interface OwnerFactoryBuilder {
    <TOptions extends OwnerFactoryDefineOptions>(options?: TOptions): OwnerFactoryInterface<{}, OwnerTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OwnerTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OwnerFactoryDefineOptions<TTransients>>(options?: TOptions) => OwnerFactoryInterface<TTransients, OwnerTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Owner} model.
 *
 * @param options
 * @returns factory {@link OwnerFactoryInterface}
 */
export declare const defineOwnerFactory: OwnerFactoryBuilder;
type OwnerHorseownerFactory = {
    _factoryFor: "Owner";
    build: () => PromiseLike<Prisma.OwnerCreateNestedOneWithoutOwnerHorsesInput["create"]>;
};
type OwnerHorsemasterHorseFactory = {
    _factoryFor: "MasterHorse";
    build: () => PromiseLike<Prisma.MasterHorseCreateNestedOneWithoutOwnerHorsesInput["create"]>;
};
type OwnerHorseFactoryDefineInput = {
    ownerHorseId?: string;
    ownerHorseInternalId?: (bigint | number);
    name?: string;
    createdAt?: Date;
    updatedAt?: Date;
    owner: OwnerHorseownerFactory | Prisma.OwnerCreateNestedOneWithoutOwnerHorsesInput;
    masterHorse: OwnerHorsemasterHorseFactory | Prisma.MasterHorseCreateNestedOneWithoutOwnerHorsesInput;
    sentReports?: Prisma.SentReportCreateNestedManyWithoutOwnerHorseInput;
};
type OwnerHorseTransientFields = Record<string, unknown> & Partial<Record<keyof OwnerHorseFactoryDefineInput, never>>;
type OwnerHorseFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OwnerHorseFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<OwnerHorse, Prisma.OwnerHorseCreateInput, TTransients>;
type OwnerHorseFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<OwnerHorseFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: OwnerHorseFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<OwnerHorse, Prisma.OwnerHorseCreateInput, TTransients>;
type OwnerHorseTraitKeys<TOptions extends OwnerHorseFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OwnerHorseFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "OwnerHorse";
    build(inputData?: Partial<Prisma.OwnerHorseCreateInput & TTransients>): PromiseLike<Prisma.OwnerHorseCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OwnerHorseCreateInput & TTransients>): PromiseLike<Prisma.OwnerHorseCreateInput>;
    buildList(list: readonly Partial<Prisma.OwnerHorseCreateInput & TTransients>[]): PromiseLike<Prisma.OwnerHorseCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OwnerHorseCreateInput & TTransients>): PromiseLike<Prisma.OwnerHorseCreateInput[]>;
    pickForConnect(inputData: OwnerHorse): Pick<OwnerHorse, "ownerHorseId">;
    create(inputData?: Partial<Prisma.OwnerHorseCreateInput & TTransients>): PromiseLike<OwnerHorse>;
    createList(list: readonly Partial<Prisma.OwnerHorseCreateInput & TTransients>[]): PromiseLike<OwnerHorse[]>;
    createList(count: number, item?: Partial<Prisma.OwnerHorseCreateInput & TTransients>): PromiseLike<OwnerHorse[]>;
    createForConnect(inputData?: Partial<Prisma.OwnerHorseCreateInput & TTransients>): PromiseLike<Pick<OwnerHorse, "ownerHorseId">>;
}
export interface OwnerHorseFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OwnerHorseFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OwnerHorseFactoryInterfaceWithoutTraits<TTransients>;
}
interface OwnerHorseFactoryBuilder {
    <TOptions extends OwnerHorseFactoryDefineOptions>(options: TOptions): OwnerHorseFactoryInterface<{}, OwnerHorseTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OwnerHorseTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OwnerHorseFactoryDefineOptions<TTransients>>(options: TOptions) => OwnerHorseFactoryInterface<TTransients, OwnerHorseTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link OwnerHorse} model.
 *
 * @param options
 * @returns factory {@link OwnerHorseFactoryInterface}
 */
export declare const defineOwnerHorseFactory: OwnerHorseFactoryBuilder;
type ReporthorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutReportsInput["create"]>;
};
type ReportorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutReportsInput["create"]>;
};
type ReportFactoryDefineInput = {
    reportId?: string;
    reportInternalId?: (bigint | number);
    title?: string;
    templateId?: string;
    isDraft?: boolean;
    printed?: boolean;
    firstSentAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    horse: ReporthorseFactory | Prisma.HorseCreateNestedOneWithoutReportsInput;
    organization: ReportorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutReportsInput;
    reportSections?: Prisma.ReportSectionCreateNestedManyWithoutReportInput;
    sentReports?: Prisma.SentReportCreateNestedManyWithoutReportInput;
    pendingSendReports?: Prisma.PendingSendReportCreateNestedManyWithoutReportInput;
    reportGenerateRequest?: Prisma.ReportGenerateRequestCreateNestedManyWithoutReportInput;
    shareReport?: Prisma.ShareReportCreateNestedManyWithoutReportInput;
};
type ReportTransientFields = Record<string, unknown> & Partial<Record<keyof ReportFactoryDefineInput, never>>;
type ReportFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Report, Prisma.ReportCreateInput, TTransients>;
type ReportFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Report, Prisma.ReportCreateInput, TTransients>;
type ReportTraitKeys<TOptions extends ReportFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Report";
    build(inputData?: Partial<Prisma.ReportCreateInput & TTransients>): PromiseLike<Prisma.ReportCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportCreateInput & TTransients>): PromiseLike<Prisma.ReportCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportCreateInput & TTransients>[]): PromiseLike<Prisma.ReportCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportCreateInput & TTransients>): PromiseLike<Prisma.ReportCreateInput[]>;
    pickForConnect(inputData: Report): Pick<Report, "reportId">;
    create(inputData?: Partial<Prisma.ReportCreateInput & TTransients>): PromiseLike<Report>;
    createList(list: readonly Partial<Prisma.ReportCreateInput & TTransients>[]): PromiseLike<Report[]>;
    createList(count: number, item?: Partial<Prisma.ReportCreateInput & TTransients>): PromiseLike<Report[]>;
    createForConnect(inputData?: Partial<Prisma.ReportCreateInput & TTransients>): PromiseLike<Pick<Report, "reportId">>;
}
export interface ReportFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportFactoryBuilder {
    <TOptions extends ReportFactoryDefineOptions>(options: TOptions): ReportFactoryInterface<{}, ReportTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportFactoryDefineOptions<TTransients>>(options: TOptions) => ReportFactoryInterface<TTransients, ReportTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Report} model.
 *
 * @param options
 * @returns factory {@link ReportFactoryInterface}
 */
export declare const defineReportFactory: ReportFactoryBuilder;
type ReportSectionreportFactory = {
    _factoryFor: "Report";
    build: () => PromiseLike<Prisma.ReportCreateNestedOneWithoutReportSectionsInput["create"]>;
};
type ReportSectionFactoryDefineInput = {
    reportSectionId?: Buffer;
    reportSectionInternalId?: (bigint | number);
    type?: string;
    templateInnerId?: string;
    createdAt?: Date;
    updatedAt?: Date;
    report: ReportSectionreportFactory | Prisma.ReportCreateNestedOneWithoutReportSectionsInput;
    reportSectionHorseConditions?: Prisma.ReportSectionHorseConditionCreateNestedManyWithoutReportSectionInput;
    reportSectionPlainTexts?: Prisma.ReportSectionPlainTextCreateNestedManyWithoutReportSectionInput;
    reportSectionImages?: Prisma.ReportSectionImageCreateNestedManyWithoutReportSectionInput;
    reportSectionWorkoutConditions?: Prisma.ReportSectionWorkoutConditionCreateNestedManyWithoutReportSectionInput;
    reportSectionMonthlySummaries?: Prisma.ReportSectionMonthlySummaryCreateNestedManyWithoutReportSectionInput;
    reportSectionMonthlyTimelines?: Prisma.ReportSectionMonthlyTimelineCreateNestedManyWithoutReportSectionInput;
    reportSectionMedicalTreatments?: Prisma.ReportSectionMedicalTreatmentCreateNestedManyWithoutReportSectionInput;
};
type ReportSectionTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionFactoryDefineInput, never>>;
type ReportSectionFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSection, Prisma.ReportSectionCreateInput, TTransients>;
type ReportSectionFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSection, Prisma.ReportSectionCreateInput, TTransients>;
type ReportSectionTraitKeys<TOptions extends ReportSectionFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSection";
    build(inputData?: Partial<Prisma.ReportSectionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionCreateInput[]>;
    pickForConnect(inputData: ReportSection): Pick<ReportSection, "reportSectionId">;
    create(inputData?: Partial<Prisma.ReportSectionCreateInput & TTransients>): PromiseLike<ReportSection>;
    createList(list: readonly Partial<Prisma.ReportSectionCreateInput & TTransients>[]): PromiseLike<ReportSection[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionCreateInput & TTransients>): PromiseLike<ReportSection[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionCreateInput & TTransients>): PromiseLike<Pick<ReportSection, "reportSectionId">>;
}
export interface ReportSectionFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionFactoryBuilder {
    <TOptions extends ReportSectionFactoryDefineOptions>(options: TOptions): ReportSectionFactoryInterface<{}, ReportSectionTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionFactoryInterface<TTransients, ReportSectionTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSection} model.
 *
 * @param options
 * @returns factory {@link ReportSectionFactoryInterface}
 */
export declare const defineReportSectionFactory: ReportSectionFactoryBuilder;
type ReportSectionPlainTextreportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionPlainTextsInput["create"]>;
};
type ReportSectionPlainTextFactoryDefineInput = {
    reportSectionPlainTextId?: Buffer;
    body?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionPlainTextreportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionPlainTextsInput;
};
type ReportSectionPlainTextTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionPlainTextFactoryDefineInput, never>>;
type ReportSectionPlainTextFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionPlainTextFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionPlainText, Prisma.ReportSectionPlainTextCreateInput, TTransients>;
type ReportSectionPlainTextFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionPlainTextFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionPlainTextFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionPlainText, Prisma.ReportSectionPlainTextCreateInput, TTransients>;
type ReportSectionPlainTextTraitKeys<TOptions extends ReportSectionPlainTextFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionPlainTextFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionPlainText";
    build(inputData?: Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionPlainTextCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionPlainTextCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionPlainTextCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionPlainTextCreateInput[]>;
    pickForConnect(inputData: ReportSectionPlainText): Pick<ReportSectionPlainText, "reportSectionPlainTextId">;
    create(inputData?: Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>): PromiseLike<ReportSectionPlainText>;
    createList(list: readonly Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>[]): PromiseLike<ReportSectionPlainText[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>): PromiseLike<ReportSectionPlainText[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionPlainTextCreateInput & TTransients>): PromiseLike<Pick<ReportSectionPlainText, "reportSectionPlainTextId">>;
}
export interface ReportSectionPlainTextFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionPlainTextFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionPlainTextFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionPlainTextFactoryBuilder {
    <TOptions extends ReportSectionPlainTextFactoryDefineOptions>(options: TOptions): ReportSectionPlainTextFactoryInterface<{}, ReportSectionPlainTextTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionPlainTextTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionPlainTextFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionPlainTextFactoryInterface<TTransients, ReportSectionPlainTextTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionPlainText} model.
 *
 * @param options
 * @returns factory {@link ReportSectionPlainTextFactoryInterface}
 */
export declare const defineReportSectionPlainTextFactory: ReportSectionPlainTextFactoryBuilder;
type ReportSectionImagereportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionImagesInput["create"]>;
};
type ReportSectionImageFactoryDefineInput = {
    reportSectionImageId?: Buffer;
    imagePath?: string | null;
    capturedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionImagereportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionImagesInput;
};
type ReportSectionImageTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionImageFactoryDefineInput, never>>;
type ReportSectionImageFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionImageFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionImage, Prisma.ReportSectionImageCreateInput, TTransients>;
type ReportSectionImageFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionImageFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionImageFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionImage, Prisma.ReportSectionImageCreateInput, TTransients>;
type ReportSectionImageTraitKeys<TOptions extends ReportSectionImageFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionImageFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionImage";
    build(inputData?: Partial<Prisma.ReportSectionImageCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionImageCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionImageCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionImageCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionImageCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionImageCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionImageCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionImageCreateInput[]>;
    pickForConnect(inputData: ReportSectionImage): Pick<ReportSectionImage, "reportSectionImageId">;
    create(inputData?: Partial<Prisma.ReportSectionImageCreateInput & TTransients>): PromiseLike<ReportSectionImage>;
    createList(list: readonly Partial<Prisma.ReportSectionImageCreateInput & TTransients>[]): PromiseLike<ReportSectionImage[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionImageCreateInput & TTransients>): PromiseLike<ReportSectionImage[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionImageCreateInput & TTransients>): PromiseLike<Pick<ReportSectionImage, "reportSectionImageId">>;
}
export interface ReportSectionImageFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionImageFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionImageFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionImageFactoryBuilder {
    <TOptions extends ReportSectionImageFactoryDefineOptions>(options: TOptions): ReportSectionImageFactoryInterface<{}, ReportSectionImageTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionImageTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionImageFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionImageFactoryInterface<TTransients, ReportSectionImageTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionImage} model.
 *
 * @param options
 * @returns factory {@link ReportSectionImageFactoryInterface}
 */
export declare const defineReportSectionImageFactory: ReportSectionImageFactoryBuilder;
type ReportSectionHorseConditionreportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionHorseConditionsInput["create"]>;
};
type ReportSectionHorseConditionFactoryDefineInput = {
    reportSectionHorseConditionId?: Buffer;
    horseWeight?: number | null;
    horseWeightMeasuredDate?: string | null;
    isGaitAbnormal?: boolean | null;
    gaitComment?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionHorseConditionreportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionHorseConditionsInput;
};
type ReportSectionHorseConditionTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionHorseConditionFactoryDefineInput, never>>;
type ReportSectionHorseConditionFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionHorseConditionFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionHorseCondition, Prisma.ReportSectionHorseConditionCreateInput, TTransients>;
type ReportSectionHorseConditionFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionHorseConditionFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionHorseConditionFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionHorseCondition, Prisma.ReportSectionHorseConditionCreateInput, TTransients>;
type ReportSectionHorseConditionTraitKeys<TOptions extends ReportSectionHorseConditionFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionHorseConditionFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionHorseCondition";
    build(inputData?: Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionHorseConditionCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionHorseConditionCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionHorseConditionCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionHorseConditionCreateInput[]>;
    pickForConnect(inputData: ReportSectionHorseCondition): Pick<ReportSectionHorseCondition, "reportSectionHorseConditionId">;
    create(inputData?: Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>): PromiseLike<ReportSectionHorseCondition>;
    createList(list: readonly Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>[]): PromiseLike<ReportSectionHorseCondition[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>): PromiseLike<ReportSectionHorseCondition[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionHorseConditionCreateInput & TTransients>): PromiseLike<Pick<ReportSectionHorseCondition, "reportSectionHorseConditionId">>;
}
export interface ReportSectionHorseConditionFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionHorseConditionFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionHorseConditionFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionHorseConditionFactoryBuilder {
    <TOptions extends ReportSectionHorseConditionFactoryDefineOptions>(options: TOptions): ReportSectionHorseConditionFactoryInterface<{}, ReportSectionHorseConditionTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionHorseConditionTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionHorseConditionFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionHorseConditionFactoryInterface<TTransients, ReportSectionHorseConditionTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionHorseCondition} model.
 *
 * @param options
 * @returns factory {@link ReportSectionHorseConditionFactoryInterface}
 */
export declare const defineReportSectionHorseConditionFactory: ReportSectionHorseConditionFactoryBuilder;
type ReportSectionWorkoutConditionreportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionWorkoutConditionsInput["create"]>;
};
type ReportSectionWorkoutConditionfacilityFactory = {
    _factoryFor: "Facility";
    build: () => PromiseLike<Prisma.FacilityCreateNestedOneWithoutReportSectionWorkoutConditionsInput["create"]>;
};
type ReportSectionWorkoutConditiontrainingCourseFactory = {
    _factoryFor: "TrainingCourse";
    build: () => PromiseLike<Prisma.TrainingCourseCreateNestedOneWithoutReportSectionWorkoutConditionsInput["create"]>;
};
type ReportSectionWorkoutConditionFactoryDefineInput = {
    reportSectionWorkoutConditionId?: Buffer;
    workoutTrainingDate?: string | null;
    riderName?: string | null;
    runningStyle?: string | null;
    workoutFurlongTime?: string | null;
    workoutFurlongPosition?: string | null;
    partnerNumber?: number | null;
    partner1Name?: string | null;
    partner2Name?: string | null;
    courseGoing?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionWorkoutConditionreportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionWorkoutConditionsInput;
    facility?: ReportSectionWorkoutConditionfacilityFactory | Prisma.FacilityCreateNestedOneWithoutReportSectionWorkoutConditionsInput;
    trainingCourse?: ReportSectionWorkoutConditiontrainingCourseFactory | Prisma.TrainingCourseCreateNestedOneWithoutReportSectionWorkoutConditionsInput;
};
type ReportSectionWorkoutConditionTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionWorkoutConditionFactoryDefineInput, never>>;
type ReportSectionWorkoutConditionFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionWorkoutConditionFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionWorkoutCondition, Prisma.ReportSectionWorkoutConditionCreateInput, TTransients>;
type ReportSectionWorkoutConditionFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionWorkoutConditionFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionWorkoutConditionFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionWorkoutCondition, Prisma.ReportSectionWorkoutConditionCreateInput, TTransients>;
type ReportSectionWorkoutConditionTraitKeys<TOptions extends ReportSectionWorkoutConditionFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionWorkoutConditionFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionWorkoutCondition";
    build(inputData?: Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionWorkoutConditionCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionWorkoutConditionCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionWorkoutConditionCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionWorkoutConditionCreateInput[]>;
    pickForConnect(inputData: ReportSectionWorkoutCondition): Pick<ReportSectionWorkoutCondition, "reportSectionWorkoutConditionId">;
    create(inputData?: Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>): PromiseLike<ReportSectionWorkoutCondition>;
    createList(list: readonly Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>[]): PromiseLike<ReportSectionWorkoutCondition[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>): PromiseLike<ReportSectionWorkoutCondition[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionWorkoutConditionCreateInput & TTransients>): PromiseLike<Pick<ReportSectionWorkoutCondition, "reportSectionWorkoutConditionId">>;
}
export interface ReportSectionWorkoutConditionFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionWorkoutConditionFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionWorkoutConditionFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionWorkoutConditionFactoryBuilder {
    <TOptions extends ReportSectionWorkoutConditionFactoryDefineOptions>(options: TOptions): ReportSectionWorkoutConditionFactoryInterface<{}, ReportSectionWorkoutConditionTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionWorkoutConditionTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionWorkoutConditionFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionWorkoutConditionFactoryInterface<TTransients, ReportSectionWorkoutConditionTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionWorkoutCondition} model.
 *
 * @param options
 * @returns factory {@link ReportSectionWorkoutConditionFactoryInterface}
 */
export declare const defineReportSectionWorkoutConditionFactory: ReportSectionWorkoutConditionFactoryBuilder;
type ReportSectionMonthlySummaryreportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionMonthlySummariesInput["create"]>;
};
type ReportSectionMonthlySummaryFactoryDefineInput = {
    reportSectionMonthlySummaryId?: Buffer;
    reportSectionMonthlySummaryInternalId?: (bigint | number);
    startYear?: number;
    startMonth?: number;
    startDay?: number;
    endYear?: number;
    endMonth?: number;
    endDay?: number;
    horseBodyWeightHistory?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionMonthlySummaryreportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionMonthlySummariesInput;
    reportSectionMonthlySummaryRaceRecords?: Prisma.ReportSectionMonthlySummaryRaceRecordCreateNestedManyWithoutReportSectionMonthlySummaryInput;
};
type ReportSectionMonthlySummaryTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMonthlySummaryFactoryDefineInput, never>>;
type ReportSectionMonthlySummaryFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMonthlySummaryFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMonthlySummary, Prisma.ReportSectionMonthlySummaryCreateInput, TTransients>;
type ReportSectionMonthlySummaryFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMonthlySummaryFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMonthlySummaryFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMonthlySummary, Prisma.ReportSectionMonthlySummaryCreateInput, TTransients>;
type ReportSectionMonthlySummaryTraitKeys<TOptions extends ReportSectionMonthlySummaryFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMonthlySummaryFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMonthlySummary";
    build(inputData?: Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlySummaryCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlySummaryCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMonthlySummaryCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlySummaryCreateInput[]>;
    pickForConnect(inputData: ReportSectionMonthlySummary): Pick<ReportSectionMonthlySummary, "reportSectionMonthlySummaryId">;
    create(inputData?: Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>): PromiseLike<ReportSectionMonthlySummary>;
    createList(list: readonly Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>[]): PromiseLike<ReportSectionMonthlySummary[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>): PromiseLike<ReportSectionMonthlySummary[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMonthlySummaryCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMonthlySummary, "reportSectionMonthlySummaryId">>;
}
export interface ReportSectionMonthlySummaryFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMonthlySummaryFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMonthlySummaryFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMonthlySummaryFactoryBuilder {
    <TOptions extends ReportSectionMonthlySummaryFactoryDefineOptions>(options: TOptions): ReportSectionMonthlySummaryFactoryInterface<{}, ReportSectionMonthlySummaryTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMonthlySummaryTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMonthlySummaryFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMonthlySummaryFactoryInterface<TTransients, ReportSectionMonthlySummaryTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMonthlySummary} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlySummaryFactoryInterface}
 */
export declare const defineReportSectionMonthlySummaryFactory: ReportSectionMonthlySummaryFactoryBuilder;
type ReportSectionMonthlySummaryRaceRecordreportSectionMonthlySummaryFactory = {
    _factoryFor: "ReportSectionMonthlySummary";
    build: () => PromiseLike<Prisma.ReportSectionMonthlySummaryCreateNestedOneWithoutReportSectionMonthlySummaryRaceRecordsInput["create"]>;
};
type ReportSectionMonthlySummaryRaceRecordFactoryDefineInput = {
    reportSectionMonthlySummaryRaceRecordId?: Buffer;
    reportSectionMonthlySummaryRaceRecordInternalId?: (bigint | number);
    year?: number;
    month?: number;
    day?: number;
    body?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSectionMonthlySummary: ReportSectionMonthlySummaryRaceRecordreportSectionMonthlySummaryFactory | Prisma.ReportSectionMonthlySummaryCreateNestedOneWithoutReportSectionMonthlySummaryRaceRecordsInput;
};
type ReportSectionMonthlySummaryRaceRecordTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMonthlySummaryRaceRecordFactoryDefineInput, never>>;
type ReportSectionMonthlySummaryRaceRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMonthlySummaryRaceRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMonthlySummaryRaceRecord, Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput, TTransients>;
type ReportSectionMonthlySummaryRaceRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMonthlySummaryRaceRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMonthlySummaryRaceRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMonthlySummaryRaceRecord, Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput, TTransients>;
type ReportSectionMonthlySummaryRaceRecordTraitKeys<TOptions extends ReportSectionMonthlySummaryRaceRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMonthlySummaryRaceRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMonthlySummaryRaceRecord";
    build(inputData?: Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput[]>;
    pickForConnect(inputData: ReportSectionMonthlySummaryRaceRecord): Pick<ReportSectionMonthlySummaryRaceRecord, "reportSectionMonthlySummaryRaceRecordId">;
    create(inputData?: Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>): PromiseLike<ReportSectionMonthlySummaryRaceRecord>;
    createList(list: readonly Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>[]): PromiseLike<ReportSectionMonthlySummaryRaceRecord[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>): PromiseLike<ReportSectionMonthlySummaryRaceRecord[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMonthlySummaryRaceRecordCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMonthlySummaryRaceRecord, "reportSectionMonthlySummaryRaceRecordId">>;
}
export interface ReportSectionMonthlySummaryRaceRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMonthlySummaryRaceRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMonthlySummaryRaceRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMonthlySummaryRaceRecordFactoryBuilder {
    <TOptions extends ReportSectionMonthlySummaryRaceRecordFactoryDefineOptions>(options: TOptions): ReportSectionMonthlySummaryRaceRecordFactoryInterface<{}, ReportSectionMonthlySummaryRaceRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMonthlySummaryRaceRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMonthlySummaryRaceRecordFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMonthlySummaryRaceRecordFactoryInterface<TTransients, ReportSectionMonthlySummaryRaceRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMonthlySummaryRaceRecord} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlySummaryRaceRecordFactoryInterface}
 */
export declare const defineReportSectionMonthlySummaryRaceRecordFactory: ReportSectionMonthlySummaryRaceRecordFactoryBuilder;
type ReportSectionMonthlyTimelinereportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionMonthlyTimelinesInput["create"]>;
};
type ReportSectionMonthlyTimelineFactoryDefineInput = {
    reportSectionMonthlyTimelineId?: Buffer;
    reportSectionMonthlyTimelineInternalId?: (bigint | number);
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionMonthlyTimelinereportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionMonthlyTimelinesInput;
    reportSectionMonthlyTimelineRecords?: Prisma.ReportSectionMonthlyTimelineRecordCreateNestedManyWithoutReportSectionMonthlyTimelineInput;
};
type ReportSectionMonthlyTimelineTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMonthlyTimelineFactoryDefineInput, never>>;
type ReportSectionMonthlyTimelineFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMonthlyTimelineFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMonthlyTimeline, Prisma.ReportSectionMonthlyTimelineCreateInput, TTransients>;
type ReportSectionMonthlyTimelineFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMonthlyTimelineFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMonthlyTimelineFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMonthlyTimeline, Prisma.ReportSectionMonthlyTimelineCreateInput, TTransients>;
type ReportSectionMonthlyTimelineTraitKeys<TOptions extends ReportSectionMonthlyTimelineFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMonthlyTimelineFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMonthlyTimeline";
    build(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlyTimelineCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlyTimelineCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMonthlyTimelineCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlyTimelineCreateInput[]>;
    pickForConnect(inputData: ReportSectionMonthlyTimeline): Pick<ReportSectionMonthlyTimeline, "reportSectionMonthlyTimelineId">;
    create(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>): PromiseLike<ReportSectionMonthlyTimeline>;
    createList(list: readonly Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>[]): PromiseLike<ReportSectionMonthlyTimeline[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>): PromiseLike<ReportSectionMonthlyTimeline[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMonthlyTimeline, "reportSectionMonthlyTimelineId">>;
}
export interface ReportSectionMonthlyTimelineFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMonthlyTimelineFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMonthlyTimelineFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMonthlyTimelineFactoryBuilder {
    <TOptions extends ReportSectionMonthlyTimelineFactoryDefineOptions>(options: TOptions): ReportSectionMonthlyTimelineFactoryInterface<{}, ReportSectionMonthlyTimelineTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMonthlyTimelineTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMonthlyTimelineFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMonthlyTimelineFactoryInterface<TTransients, ReportSectionMonthlyTimelineTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMonthlyTimeline} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlyTimelineFactoryInterface}
 */
export declare const defineReportSectionMonthlyTimelineFactory: ReportSectionMonthlyTimelineFactoryBuilder;
type ReportSectionMonthlyTimelineRecordreportSectionMonthlyTimelineFactory = {
    _factoryFor: "ReportSectionMonthlyTimeline";
    build: () => PromiseLike<Prisma.ReportSectionMonthlyTimelineCreateNestedOneWithoutReportSectionMonthlyTimelineRecordsInput["create"]>;
};
type ReportSectionMonthlyTimelineRecordFactoryDefineInput = {
    reportSectionMonthlyTimelineRecordId?: Buffer;
    reportSectionMonthlyTimelineRecordInternalId?: (bigint | number);
    year?: number;
    month?: number;
    day?: number;
    body?: string | null;
    trainingMenu?: string | null;
    assignee?: string | null;
    furlongTime?: string | null;
    index?: string;
    createdAt?: Date;
    updatedAt?: Date;
    reportSectionMonthlyTimeline: ReportSectionMonthlyTimelineRecordreportSectionMonthlyTimelineFactory | Prisma.ReportSectionMonthlyTimelineCreateNestedOneWithoutReportSectionMonthlyTimelineRecordsInput;
};
type ReportSectionMonthlyTimelineRecordTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMonthlyTimelineRecordFactoryDefineInput, never>>;
type ReportSectionMonthlyTimelineRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMonthlyTimelineRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMonthlyTimelineRecord, Prisma.ReportSectionMonthlyTimelineRecordCreateInput, TTransients>;
type ReportSectionMonthlyTimelineRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMonthlyTimelineRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMonthlyTimelineRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMonthlyTimelineRecord, Prisma.ReportSectionMonthlyTimelineRecordCreateInput, TTransients>;
type ReportSectionMonthlyTimelineRecordTraitKeys<TOptions extends ReportSectionMonthlyTimelineRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMonthlyTimelineRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMonthlyTimelineRecord";
    build(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlyTimelineRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlyTimelineRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMonthlyTimelineRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMonthlyTimelineRecordCreateInput[]>;
    pickForConnect(inputData: ReportSectionMonthlyTimelineRecord): Pick<ReportSectionMonthlyTimelineRecord, "reportSectionMonthlyTimelineRecordId">;
    create(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>): PromiseLike<ReportSectionMonthlyTimelineRecord>;
    createList(list: readonly Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>[]): PromiseLike<ReportSectionMonthlyTimelineRecord[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>): PromiseLike<ReportSectionMonthlyTimelineRecord[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMonthlyTimelineRecordCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMonthlyTimelineRecord, "reportSectionMonthlyTimelineRecordId">>;
}
export interface ReportSectionMonthlyTimelineRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMonthlyTimelineRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMonthlyTimelineRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMonthlyTimelineRecordFactoryBuilder {
    <TOptions extends ReportSectionMonthlyTimelineRecordFactoryDefineOptions>(options: TOptions): ReportSectionMonthlyTimelineRecordFactoryInterface<{}, ReportSectionMonthlyTimelineRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMonthlyTimelineRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMonthlyTimelineRecordFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMonthlyTimelineRecordFactoryInterface<TTransients, ReportSectionMonthlyTimelineRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMonthlyTimelineRecord} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlyTimelineRecordFactoryInterface}
 */
export declare const defineReportSectionMonthlyTimelineRecordFactory: ReportSectionMonthlyTimelineRecordFactoryBuilder;
type ReportSectionMedicalTreatmentreportSectionFactory = {
    _factoryFor: "ReportSection";
    build: () => PromiseLike<Prisma.ReportSectionCreateNestedOneWithoutReportSectionMedicalTreatmentsInput["create"]>;
};
type ReportSectionMedicalTreatmentFactoryDefineInput = {
    reportSectionMedicalTreatmentId?: Buffer;
    reportSectionMedicalTreatmentInternalId?: (bigint | number);
    createdAt?: Date;
    updatedAt?: Date;
    reportSection: ReportSectionMedicalTreatmentreportSectionFactory | Prisma.ReportSectionCreateNestedOneWithoutReportSectionMedicalTreatmentsInput;
    reportSectionMedicalTreatmentRecords?: Prisma.ReportSectionMedicalTreatmentRecordCreateNestedManyWithoutReportSectionMedicalTreatmentInput;
};
type ReportSectionMedicalTreatmentTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMedicalTreatmentFactoryDefineInput, never>>;
type ReportSectionMedicalTreatmentFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMedicalTreatmentFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMedicalTreatment, Prisma.ReportSectionMedicalTreatmentCreateInput, TTransients>;
type ReportSectionMedicalTreatmentFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMedicalTreatmentFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMedicalTreatmentFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMedicalTreatment, Prisma.ReportSectionMedicalTreatmentCreateInput, TTransients>;
type ReportSectionMedicalTreatmentTraitKeys<TOptions extends ReportSectionMedicalTreatmentFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMedicalTreatmentFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMedicalTreatment";
    build(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMedicalTreatmentCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentCreateInput[]>;
    pickForConnect(inputData: ReportSectionMedicalTreatment): Pick<ReportSectionMedicalTreatment, "reportSectionMedicalTreatmentId">;
    create(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>): PromiseLike<ReportSectionMedicalTreatment>;
    createList(list: readonly Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>[]): PromiseLike<ReportSectionMedicalTreatment[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>): PromiseLike<ReportSectionMedicalTreatment[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMedicalTreatment, "reportSectionMedicalTreatmentId">>;
}
export interface ReportSectionMedicalTreatmentFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMedicalTreatmentFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMedicalTreatmentFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMedicalTreatmentFactoryBuilder {
    <TOptions extends ReportSectionMedicalTreatmentFactoryDefineOptions>(options: TOptions): ReportSectionMedicalTreatmentFactoryInterface<{}, ReportSectionMedicalTreatmentTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMedicalTreatmentTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMedicalTreatmentFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMedicalTreatmentFactoryInterface<TTransients, ReportSectionMedicalTreatmentTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMedicalTreatment} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMedicalTreatmentFactoryInterface}
 */
export declare const defineReportSectionMedicalTreatmentFactory: ReportSectionMedicalTreatmentFactoryBuilder;
type ReportSectionMedicalTreatmentRecordreportSectionMedicalTreatmentFactory = {
    _factoryFor: "ReportSectionMedicalTreatment";
    build: () => PromiseLike<Prisma.ReportSectionMedicalTreatmentCreateNestedOneWithoutReportSectionMedicalTreatmentRecordsInput["create"]>;
};
type ReportSectionMedicalTreatmentRecordFactoryDefineInput = {
    reportSectionMedicalTreatmentRecordId?: Buffer;
    reportSectionMedicalTreatmentRecordInternalId?: (bigint | number);
    year?: number;
    month?: number;
    day?: number;
    body?: string | null;
    veterinarian?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    reportSectionMedicalTreatment: ReportSectionMedicalTreatmentRecordreportSectionMedicalTreatmentFactory | Prisma.ReportSectionMedicalTreatmentCreateNestedOneWithoutReportSectionMedicalTreatmentRecordsInput;
    reportSectionMedicalTreatmentAffectedAreaPhotos?: Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateNestedManyWithoutReportSectionMedicalTreatmentRecordInput;
};
type ReportSectionMedicalTreatmentRecordTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMedicalTreatmentRecordFactoryDefineInput, never>>;
type ReportSectionMedicalTreatmentRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMedicalTreatmentRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMedicalTreatmentRecord, Prisma.ReportSectionMedicalTreatmentRecordCreateInput, TTransients>;
type ReportSectionMedicalTreatmentRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMedicalTreatmentRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMedicalTreatmentRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMedicalTreatmentRecord, Prisma.ReportSectionMedicalTreatmentRecordCreateInput, TTransients>;
type ReportSectionMedicalTreatmentRecordTraitKeys<TOptions extends ReportSectionMedicalTreatmentRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMedicalTreatmentRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMedicalTreatmentRecord";
    build(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMedicalTreatmentRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentRecordCreateInput[]>;
    pickForConnect(inputData: ReportSectionMedicalTreatmentRecord): Pick<ReportSectionMedicalTreatmentRecord, "reportSectionMedicalTreatmentRecordId">;
    create(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<ReportSectionMedicalTreatmentRecord>;
    createList(list: readonly Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>[]): PromiseLike<ReportSectionMedicalTreatmentRecord[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<ReportSectionMedicalTreatmentRecord[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentRecordCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMedicalTreatmentRecord, "reportSectionMedicalTreatmentRecordId">>;
}
export interface ReportSectionMedicalTreatmentRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMedicalTreatmentRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMedicalTreatmentRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMedicalTreatmentRecordFactoryBuilder {
    <TOptions extends ReportSectionMedicalTreatmentRecordFactoryDefineOptions>(options: TOptions): ReportSectionMedicalTreatmentRecordFactoryInterface<{}, ReportSectionMedicalTreatmentRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMedicalTreatmentRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMedicalTreatmentRecordFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMedicalTreatmentRecordFactoryInterface<TTransients, ReportSectionMedicalTreatmentRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMedicalTreatmentRecord} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMedicalTreatmentRecordFactoryInterface}
 */
export declare const defineReportSectionMedicalTreatmentRecordFactory: ReportSectionMedicalTreatmentRecordFactoryBuilder;
type ReportSectionMedicalTreatmentAffectedAreaPhotoreportSectionMedicalTreatmentRecordFactory = {
    _factoryFor: "ReportSectionMedicalTreatmentRecord";
    build: () => PromiseLike<Prisma.ReportSectionMedicalTreatmentRecordCreateNestedOneWithoutReportSectionMedicalTreatmentAffectedAreaPhotosInput["create"]>;
};
type ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineInput = {
    reportSectionMedicalTreatmentAffectedAreaPhotoId?: Buffer;
    reportSectionMedicalTreatmentAffectedAreaPhotoInternalId?: (bigint | number);
    photoPath?: string;
    createdAt?: Date;
    updatedAt?: Date;
    reportSectionMedicalTreatmentRecord: ReportSectionMedicalTreatmentAffectedAreaPhotoreportSectionMedicalTreatmentRecordFactory | Prisma.ReportSectionMedicalTreatmentRecordCreateNestedOneWithoutReportSectionMedicalTreatmentAffectedAreaPhotosInput;
};
type ReportSectionMedicalTreatmentAffectedAreaPhotoTransientFields = Record<string, unknown> & Partial<Record<keyof ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineInput, never>>;
type ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportSectionMedicalTreatmentAffectedAreaPhoto, Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput, TTransients>;
type ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportSectionMedicalTreatmentAffectedAreaPhoto, Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput, TTransients>;
type ReportSectionMedicalTreatmentAffectedAreaPhotoTraitKeys<TOptions extends ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportSectionMedicalTreatmentAffectedAreaPhoto";
    build(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>[]): PromiseLike<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput[]>;
    pickForConnect(inputData: ReportSectionMedicalTreatmentAffectedAreaPhoto): Pick<ReportSectionMedicalTreatmentAffectedAreaPhoto, "reportSectionMedicalTreatmentAffectedAreaPhotoId">;
    create(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<ReportSectionMedicalTreatmentAffectedAreaPhoto>;
    createList(list: readonly Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>[]): PromiseLike<ReportSectionMedicalTreatmentAffectedAreaPhoto[]>;
    createList(count: number, item?: Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<ReportSectionMedicalTreatmentAffectedAreaPhoto[]>;
    createForConnect(inputData?: Partial<Prisma.ReportSectionMedicalTreatmentAffectedAreaPhotoCreateInput & TTransients>): PromiseLike<Pick<ReportSectionMedicalTreatmentAffectedAreaPhoto, "reportSectionMedicalTreatmentAffectedAreaPhotoId">>;
}
export interface ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryBuilder {
    <TOptions extends ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions>(options: TOptions): ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterface<{}, ReportSectionMedicalTreatmentAffectedAreaPhotoTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportSectionMedicalTreatmentAffectedAreaPhotoTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryDefineOptions<TTransients>>(options: TOptions) => ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterface<TTransients, ReportSectionMedicalTreatmentAffectedAreaPhotoTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportSectionMedicalTreatmentAffectedAreaPhoto} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterface}
 */
export declare const defineReportSectionMedicalTreatmentAffectedAreaPhotoFactory: ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryBuilder;
type SentReportreportFactory = {
    _factoryFor: "Report";
    build: () => PromiseLike<Prisma.ReportCreateNestedOneWithoutSentReportsInput["create"]>;
};
type SentReportorganizationOwnerFactory = {
    _factoryFor: "OrganizationOwner";
    build: () => PromiseLike<Prisma.OrganizationOwnerCreateNestedOneWithoutSentReportsInput["create"]>;
};
type SentReportownerHorseFactory = {
    _factoryFor: "OwnerHorse";
    build: () => PromiseLike<Prisma.OwnerHorseCreateNestedOneWithoutSentReportsInput["create"]>;
};
type SentReportFactoryDefineInput = {
    sentReportId?: string;
    sentReportInternalId?: (bigint | number);
    sentAt?: Date;
    firstReadAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    report: SentReportreportFactory | Prisma.ReportCreateNestedOneWithoutSentReportsInput;
    organizationOwner: SentReportorganizationOwnerFactory | Prisma.OrganizationOwnerCreateNestedOneWithoutSentReportsInput;
    ownerHorse: SentReportownerHorseFactory | Prisma.OwnerHorseCreateNestedOneWithoutSentReportsInput;
};
type SentReportTransientFields = Record<string, unknown> & Partial<Record<keyof SentReportFactoryDefineInput, never>>;
type SentReportFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<SentReportFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<SentReport, Prisma.SentReportCreateInput, TTransients>;
type SentReportFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<SentReportFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: SentReportFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<SentReport, Prisma.SentReportCreateInput, TTransients>;
type SentReportTraitKeys<TOptions extends SentReportFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface SentReportFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "SentReport";
    build(inputData?: Partial<Prisma.SentReportCreateInput & TTransients>): PromiseLike<Prisma.SentReportCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.SentReportCreateInput & TTransients>): PromiseLike<Prisma.SentReportCreateInput>;
    buildList(list: readonly Partial<Prisma.SentReportCreateInput & TTransients>[]): PromiseLike<Prisma.SentReportCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.SentReportCreateInput & TTransients>): PromiseLike<Prisma.SentReportCreateInput[]>;
    pickForConnect(inputData: SentReport): Pick<SentReport, "sentReportId">;
    create(inputData?: Partial<Prisma.SentReportCreateInput & TTransients>): PromiseLike<SentReport>;
    createList(list: readonly Partial<Prisma.SentReportCreateInput & TTransients>[]): PromiseLike<SentReport[]>;
    createList(count: number, item?: Partial<Prisma.SentReportCreateInput & TTransients>): PromiseLike<SentReport[]>;
    createForConnect(inputData?: Partial<Prisma.SentReportCreateInput & TTransients>): PromiseLike<Pick<SentReport, "sentReportId">>;
}
export interface SentReportFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends SentReportFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): SentReportFactoryInterfaceWithoutTraits<TTransients>;
}
interface SentReportFactoryBuilder {
    <TOptions extends SentReportFactoryDefineOptions>(options: TOptions): SentReportFactoryInterface<{}, SentReportTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends SentReportTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends SentReportFactoryDefineOptions<TTransients>>(options: TOptions) => SentReportFactoryInterface<TTransients, SentReportTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link SentReport} model.
 *
 * @param options
 * @returns factory {@link SentReportFactoryInterface}
 */
export declare const defineSentReportFactory: SentReportFactoryBuilder;
type ShareReportreportFactory = {
    _factoryFor: "Report";
    build: () => PromiseLike<Prisma.ReportCreateNestedOneWithoutShareReportInput["create"]>;
};
type ShareReportFactoryDefineInput = {
    shareReportId?: string;
    shareReportInternalId?: (bigint | number);
    createdAt?: Date;
    updatedAt?: Date;
    report: ShareReportreportFactory | Prisma.ReportCreateNestedOneWithoutShareReportInput;
};
type ShareReportTransientFields = Record<string, unknown> & Partial<Record<keyof ShareReportFactoryDefineInput, never>>;
type ShareReportFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ShareReportFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ShareReport, Prisma.ShareReportCreateInput, TTransients>;
type ShareReportFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ShareReportFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ShareReportFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ShareReport, Prisma.ShareReportCreateInput, TTransients>;
type ShareReportTraitKeys<TOptions extends ShareReportFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ShareReportFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ShareReport";
    build(inputData?: Partial<Prisma.ShareReportCreateInput & TTransients>): PromiseLike<Prisma.ShareReportCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ShareReportCreateInput & TTransients>): PromiseLike<Prisma.ShareReportCreateInput>;
    buildList(list: readonly Partial<Prisma.ShareReportCreateInput & TTransients>[]): PromiseLike<Prisma.ShareReportCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ShareReportCreateInput & TTransients>): PromiseLike<Prisma.ShareReportCreateInput[]>;
    pickForConnect(inputData: ShareReport): Pick<ShareReport, "shareReportId">;
    create(inputData?: Partial<Prisma.ShareReportCreateInput & TTransients>): PromiseLike<ShareReport>;
    createList(list: readonly Partial<Prisma.ShareReportCreateInput & TTransients>[]): PromiseLike<ShareReport[]>;
    createList(count: number, item?: Partial<Prisma.ShareReportCreateInput & TTransients>): PromiseLike<ShareReport[]>;
    createForConnect(inputData?: Partial<Prisma.ShareReportCreateInput & TTransients>): PromiseLike<Pick<ShareReport, "shareReportId">>;
}
export interface ShareReportFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ShareReportFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ShareReportFactoryInterfaceWithoutTraits<TTransients>;
}
interface ShareReportFactoryBuilder {
    <TOptions extends ShareReportFactoryDefineOptions>(options: TOptions): ShareReportFactoryInterface<{}, ShareReportTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ShareReportTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ShareReportFactoryDefineOptions<TTransients>>(options: TOptions) => ShareReportFactoryInterface<TTransients, ShareReportTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ShareReport} model.
 *
 * @param options
 * @returns factory {@link ShareReportFactoryInterface}
 */
export declare const defineShareReportFactory: ShareReportFactoryBuilder;
type PendingSendReportreportFactory = {
    _factoryFor: "Report";
    build: () => PromiseLike<Prisma.ReportCreateNestedOneWithoutPendingSendReportsInput["create"]>;
};
type PendingSendReportorganizationOwnerFactory = {
    _factoryFor: "OrganizationOwner";
    build: () => PromiseLike<Prisma.OrganizationOwnerCreateNestedOneWithoutPendingSendReportsInput["create"]>;
};
type PendingSendReporthorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutPendingSendReportsInput["create"]>;
};
type PendingSendReportFactoryDefineInput = {
    pendingSendReportInternalId?: (bigint | number);
    sentAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    report: PendingSendReportreportFactory | Prisma.ReportCreateNestedOneWithoutPendingSendReportsInput;
    organizationOwner: PendingSendReportorganizationOwnerFactory | Prisma.OrganizationOwnerCreateNestedOneWithoutPendingSendReportsInput;
    horse: PendingSendReporthorseFactory | Prisma.HorseCreateNestedOneWithoutPendingSendReportsInput;
};
type PendingSendReportTransientFields = Record<string, unknown> & Partial<Record<keyof PendingSendReportFactoryDefineInput, never>>;
type PendingSendReportFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<PendingSendReportFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<PendingSendReport, Prisma.PendingSendReportCreateInput, TTransients>;
type PendingSendReportFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<PendingSendReportFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: PendingSendReportFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<PendingSendReport, Prisma.PendingSendReportCreateInput, TTransients>;
type PendingSendReportTraitKeys<TOptions extends PendingSendReportFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface PendingSendReportFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "PendingSendReport";
    build(inputData?: Partial<Prisma.PendingSendReportCreateInput & TTransients>): PromiseLike<Prisma.PendingSendReportCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.PendingSendReportCreateInput & TTransients>): PromiseLike<Prisma.PendingSendReportCreateInput>;
    buildList(list: readonly Partial<Prisma.PendingSendReportCreateInput & TTransients>[]): PromiseLike<Prisma.PendingSendReportCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.PendingSendReportCreateInput & TTransients>): PromiseLike<Prisma.PendingSendReportCreateInput[]>;
    pickForConnect(inputData: PendingSendReport): Pick<PendingSendReport, "pendingSendReportInternalId">;
    create(inputData?: Partial<Prisma.PendingSendReportCreateInput & TTransients>): PromiseLike<PendingSendReport>;
    createList(list: readonly Partial<Prisma.PendingSendReportCreateInput & TTransients>[]): PromiseLike<PendingSendReport[]>;
    createList(count: number, item?: Partial<Prisma.PendingSendReportCreateInput & TTransients>): PromiseLike<PendingSendReport[]>;
    createForConnect(inputData?: Partial<Prisma.PendingSendReportCreateInput & TTransients>): PromiseLike<Pick<PendingSendReport, "pendingSendReportInternalId">>;
}
export interface PendingSendReportFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends PendingSendReportFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): PendingSendReportFactoryInterfaceWithoutTraits<TTransients>;
}
interface PendingSendReportFactoryBuilder {
    <TOptions extends PendingSendReportFactoryDefineOptions>(options: TOptions): PendingSendReportFactoryInterface<{}, PendingSendReportTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends PendingSendReportTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends PendingSendReportFactoryDefineOptions<TTransients>>(options: TOptions) => PendingSendReportFactoryInterface<TTransients, PendingSendReportTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link PendingSendReport} model.
 *
 * @param options
 * @returns factory {@link PendingSendReportFactoryInterface}
 */
export declare const definePendingSendReportFactory: PendingSendReportFactoryBuilder;
type ReportGenerateRequesthorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutReportGenerateRequestInput["create"]>;
};
type ReportGenerateRequestreportFactory = {
    _factoryFor: "Report";
    build: () => PromiseLike<Prisma.ReportCreateNestedOneWithoutReportGenerateRequestInput["create"]>;
};
type ReportGenerateRequestaiGenerateLogFactory = {
    _factoryFor: "AiGenerateLog";
    build: () => PromiseLike<Prisma.AiGenerateLogCreateNestedOneWithoutReportGenerateRequestsInput["create"]>;
};
type ReportGenerateRequestFactoryDefineInput = {
    reportGenerateRequestId?: string;
    reportGenerateRequestInternalId?: (bigint | number);
    requestMemo?: string | null;
    generatedContent?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    horse: ReportGenerateRequesthorseFactory | Prisma.HorseCreateNestedOneWithoutReportGenerateRequestInput;
    report?: ReportGenerateRequestreportFactory | Prisma.ReportCreateNestedOneWithoutReportGenerateRequestInput;
    aiGenerateLog?: ReportGenerateRequestaiGenerateLogFactory | Prisma.AiGenerateLogCreateNestedOneWithoutReportGenerateRequestsInput;
};
type ReportGenerateRequestTransientFields = Record<string, unknown> & Partial<Record<keyof ReportGenerateRequestFactoryDefineInput, never>>;
type ReportGenerateRequestFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<ReportGenerateRequestFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<ReportGenerateRequest, Prisma.ReportGenerateRequestCreateInput, TTransients>;
type ReportGenerateRequestFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<ReportGenerateRequestFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: ReportGenerateRequestFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<ReportGenerateRequest, Prisma.ReportGenerateRequestCreateInput, TTransients>;
type ReportGenerateRequestTraitKeys<TOptions extends ReportGenerateRequestFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface ReportGenerateRequestFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "ReportGenerateRequest";
    build(inputData?: Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>): PromiseLike<Prisma.ReportGenerateRequestCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>): PromiseLike<Prisma.ReportGenerateRequestCreateInput>;
    buildList(list: readonly Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>[]): PromiseLike<Prisma.ReportGenerateRequestCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>): PromiseLike<Prisma.ReportGenerateRequestCreateInput[]>;
    pickForConnect(inputData: ReportGenerateRequest): Pick<ReportGenerateRequest, "reportGenerateRequestId">;
    create(inputData?: Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>): PromiseLike<ReportGenerateRequest>;
    createList(list: readonly Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>[]): PromiseLike<ReportGenerateRequest[]>;
    createList(count: number, item?: Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>): PromiseLike<ReportGenerateRequest[]>;
    createForConnect(inputData?: Partial<Prisma.ReportGenerateRequestCreateInput & TTransients>): PromiseLike<Pick<ReportGenerateRequest, "reportGenerateRequestId">>;
}
export interface ReportGenerateRequestFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends ReportGenerateRequestFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): ReportGenerateRequestFactoryInterfaceWithoutTraits<TTransients>;
}
interface ReportGenerateRequestFactoryBuilder {
    <TOptions extends ReportGenerateRequestFactoryDefineOptions>(options: TOptions): ReportGenerateRequestFactoryInterface<{}, ReportGenerateRequestTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends ReportGenerateRequestTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends ReportGenerateRequestFactoryDefineOptions<TTransients>>(options: TOptions) => ReportGenerateRequestFactoryInterface<TTransients, ReportGenerateRequestTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link ReportGenerateRequest} model.
 *
 * @param options
 * @returns factory {@link ReportGenerateRequestFactoryInterface}
 */
export declare const defineReportGenerateRequestFactory: ReportGenerateRequestFactoryBuilder;
type StableTmOutsideFarmorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutStableTmOutsideFarmInput["create"]>;
};
type StableTmOutsideFarmfarmAreaFactory = {
    _factoryFor: "FarmArea";
    build: () => PromiseLike<Prisma.FarmAreaCreateNestedOneWithoutStableTmOutsideFarmInput["create"]>;
};
type StableTmOutsideFarmmasterFarmFactory = {
    _factoryFor: "MasterFarm";
    build: () => PromiseLike<Prisma.MasterFarmCreateNestedOneWithoutStableTmOutsideFarmsInput["create"]>;
};
type StableTmOutsideFarmFactoryDefineInput = {
    outsideFarmId?: Buffer;
    outsideFarmInternalId?: (bigint | number);
    name?: string;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    organization: StableTmOutsideFarmorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutStableTmOutsideFarmInput;
    farmArea: StableTmOutsideFarmfarmAreaFactory | Prisma.FarmAreaCreateNestedOneWithoutStableTmOutsideFarmInput;
    masterFarm?: StableTmOutsideFarmmasterFarmFactory | Prisma.MasterFarmCreateNestedOneWithoutStableTmOutsideFarmsInput;
    StableTmTransportOutStatus?: Prisma.StableTmTransportOutStatusCreateNestedManyWithoutFarmInput;
    HorseStatus?: Prisma.HorseStatusCreateNestedManyWithoutOutsideFarmInput;
};
type StableTmOutsideFarmTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmOutsideFarmFactoryDefineInput, never>>;
type StableTmOutsideFarmFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmOutsideFarmFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmOutsideFarm, Prisma.StableTmOutsideFarmCreateInput, TTransients>;
type StableTmOutsideFarmFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmOutsideFarmFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmOutsideFarmFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmOutsideFarm, Prisma.StableTmOutsideFarmCreateInput, TTransients>;
type StableTmOutsideFarmTraitKeys<TOptions extends StableTmOutsideFarmFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmOutsideFarmFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmOutsideFarm";
    build(inputData?: Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>): PromiseLike<Prisma.StableTmOutsideFarmCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>): PromiseLike<Prisma.StableTmOutsideFarmCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmOutsideFarmCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>): PromiseLike<Prisma.StableTmOutsideFarmCreateInput[]>;
    pickForConnect(inputData: StableTmOutsideFarm): Pick<StableTmOutsideFarm, "outsideFarmId">;
    create(inputData?: Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>): PromiseLike<StableTmOutsideFarm>;
    createList(list: readonly Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>[]): PromiseLike<StableTmOutsideFarm[]>;
    createList(count: number, item?: Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>): PromiseLike<StableTmOutsideFarm[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmOutsideFarmCreateInput & TTransients>): PromiseLike<Pick<StableTmOutsideFarm, "outsideFarmId">>;
}
export interface StableTmOutsideFarmFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmOutsideFarmFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmOutsideFarmFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmOutsideFarmFactoryBuilder {
    <TOptions extends StableTmOutsideFarmFactoryDefineOptions>(options: TOptions): StableTmOutsideFarmFactoryInterface<{}, StableTmOutsideFarmTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmOutsideFarmTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmOutsideFarmFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmOutsideFarmFactoryInterface<TTransients, StableTmOutsideFarmTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmOutsideFarm} model.
 *
 * @param options
 * @returns factory {@link StableTmOutsideFarmFactoryInterface}
 */
export declare const defineStableTmOutsideFarmFactory: StableTmOutsideFarmFactoryBuilder;
type StableTmSectionstableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutStableTmSectionsInput["create"]>;
};
type StableTmSectionFactoryDefineInput = {
    sectionId?: Buffer;
    sectionInternalId?: (bigint | number);
    startDate?: Date;
    endDate?: Date;
    status?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    stable: StableTmSectionstableFactory | Prisma.StableCreateNestedOneWithoutStableTmSectionsInput;
    transportQueueTickets?: Prisma.StableTmTransportQueueTicketCreateNestedManyWithoutSectionInput;
    fixedSlots?: Prisma.StableTmFixedSlotCreateNestedManyWithoutSectionInput;
    transportDailyRecords?: Prisma.StableTmTransportDailyRecordCreateNestedManyWithoutSectionInput;
};
type StableTmSectionTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmSectionFactoryDefineInput, never>>;
type StableTmSectionFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmSectionFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmSection, Prisma.StableTmSectionCreateInput, TTransients>;
type StableTmSectionFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmSectionFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmSectionFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmSection, Prisma.StableTmSectionCreateInput, TTransients>;
type StableTmSectionTraitKeys<TOptions extends StableTmSectionFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmSectionFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmSection";
    build(inputData?: Partial<Prisma.StableTmSectionCreateInput & TTransients>): PromiseLike<Prisma.StableTmSectionCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmSectionCreateInput & TTransients>): PromiseLike<Prisma.StableTmSectionCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmSectionCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmSectionCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmSectionCreateInput & TTransients>): PromiseLike<Prisma.StableTmSectionCreateInput[]>;
    pickForConnect(inputData: StableTmSection): Pick<StableTmSection, "sectionId">;
    create(inputData?: Partial<Prisma.StableTmSectionCreateInput & TTransients>): PromiseLike<StableTmSection>;
    createList(list: readonly Partial<Prisma.StableTmSectionCreateInput & TTransients>[]): PromiseLike<StableTmSection[]>;
    createList(count: number, item?: Partial<Prisma.StableTmSectionCreateInput & TTransients>): PromiseLike<StableTmSection[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmSectionCreateInput & TTransients>): PromiseLike<Pick<StableTmSection, "sectionId">>;
}
export interface StableTmSectionFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmSectionFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmSectionFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmSectionFactoryBuilder {
    <TOptions extends StableTmSectionFactoryDefineOptions>(options: TOptions): StableTmSectionFactoryInterface<{}, StableTmSectionTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmSectionTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmSectionFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmSectionFactoryInterface<TTransients, StableTmSectionTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmSection} model.
 *
 * @param options
 * @returns factory {@link StableTmSectionFactoryInterface}
 */
export declare const defineStableTmSectionFactory: StableTmSectionFactoryBuilder;
type StableTmFixedSlotsectionFactory = {
    _factoryFor: "StableTmSection";
    build: () => PromiseLike<Prisma.StableTmSectionCreateNestedOneWithoutFixedSlotsInput["create"]>;
};
type StableTmFixedSlotFactoryDefineInput = {
    fixedSlotId?: Buffer;
    fixedSlotInternalId?: (bigint | number);
    numberOfSection?: number;
    slotNum?: number;
    createdAt?: Date;
    updatedAt?: Date;
    section: StableTmFixedSlotsectionFactory | Prisma.StableTmSectionCreateNestedOneWithoutFixedSlotsInput;
};
type StableTmFixedSlotTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmFixedSlotFactoryDefineInput, never>>;
type StableTmFixedSlotFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmFixedSlotFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmFixedSlot, Prisma.StableTmFixedSlotCreateInput, TTransients>;
type StableTmFixedSlotFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmFixedSlotFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmFixedSlotFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmFixedSlot, Prisma.StableTmFixedSlotCreateInput, TTransients>;
type StableTmFixedSlotTraitKeys<TOptions extends StableTmFixedSlotFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmFixedSlotFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmFixedSlot";
    build(inputData?: Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>): PromiseLike<Prisma.StableTmFixedSlotCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>): PromiseLike<Prisma.StableTmFixedSlotCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmFixedSlotCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>): PromiseLike<Prisma.StableTmFixedSlotCreateInput[]>;
    pickForConnect(inputData: StableTmFixedSlot): Pick<StableTmFixedSlot, "fixedSlotId">;
    create(inputData?: Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>): PromiseLike<StableTmFixedSlot>;
    createList(list: readonly Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>[]): PromiseLike<StableTmFixedSlot[]>;
    createList(count: number, item?: Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>): PromiseLike<StableTmFixedSlot[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmFixedSlotCreateInput & TTransients>): PromiseLike<Pick<StableTmFixedSlot, "fixedSlotId">>;
}
export interface StableTmFixedSlotFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmFixedSlotFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmFixedSlotFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmFixedSlotFactoryBuilder {
    <TOptions extends StableTmFixedSlotFactoryDefineOptions>(options: TOptions): StableTmFixedSlotFactoryInterface<{}, StableTmFixedSlotTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmFixedSlotTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmFixedSlotFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmFixedSlotFactoryInterface<TTransients, StableTmFixedSlotTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmFixedSlot} model.
 *
 * @param options
 * @returns factory {@link StableTmFixedSlotFactoryInterface}
 */
export declare const defineStableTmFixedSlotFactory: StableTmFixedSlotFactoryBuilder;
type StableTmTransportQueueTicketsectionFactory = {
    _factoryFor: "StableTmSection";
    build: () => PromiseLike<Prisma.StableTmSectionCreateNestedOneWithoutTransportQueueTicketsInput["create"]>;
};
type StableTmTransportQueueTicketFactoryDefineInput = {
    transportQueueTicketId?: Buffer;
    transportQueueTicketInternalId?: (bigint | number);
    ticketKey?: string;
    createdAt?: Date;
    updatedAt?: Date;
    section: StableTmTransportQueueTicketsectionFactory | Prisma.StableTmSectionCreateNestedOneWithoutTransportQueueTicketsInput;
};
type StableTmTransportQueueTicketTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmTransportQueueTicketFactoryDefineInput, never>>;
type StableTmTransportQueueTicketFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmTransportQueueTicketFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmTransportQueueTicket, Prisma.StableTmTransportQueueTicketCreateInput, TTransients>;
type StableTmTransportQueueTicketFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmTransportQueueTicketFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmTransportQueueTicketFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmTransportQueueTicket, Prisma.StableTmTransportQueueTicketCreateInput, TTransients>;
type StableTmTransportQueueTicketTraitKeys<TOptions extends StableTmTransportQueueTicketFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmTransportQueueTicketFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmTransportQueueTicket";
    build(inputData?: Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportQueueTicketCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportQueueTicketCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmTransportQueueTicketCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportQueueTicketCreateInput[]>;
    pickForConnect(inputData: StableTmTransportQueueTicket): Pick<StableTmTransportQueueTicket, "transportQueueTicketId">;
    create(inputData?: Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>): PromiseLike<StableTmTransportQueueTicket>;
    createList(list: readonly Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>[]): PromiseLike<StableTmTransportQueueTicket[]>;
    createList(count: number, item?: Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>): PromiseLike<StableTmTransportQueueTicket[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmTransportQueueTicketCreateInput & TTransients>): PromiseLike<Pick<StableTmTransportQueueTicket, "transportQueueTicketId">>;
}
export interface StableTmTransportQueueTicketFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmTransportQueueTicketFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmTransportQueueTicketFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmTransportQueueTicketFactoryBuilder {
    <TOptions extends StableTmTransportQueueTicketFactoryDefineOptions>(options: TOptions): StableTmTransportQueueTicketFactoryInterface<{}, StableTmTransportQueueTicketTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmTransportQueueTicketTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmTransportQueueTicketFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmTransportQueueTicketFactoryInterface<TTransients, StableTmTransportQueueTicketTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmTransportQueueTicket} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportQueueTicketFactoryInterface}
 */
export declare const defineStableTmTransportQueueTicketFactory: StableTmTransportQueueTicketFactoryBuilder;
type StableTmTransportDailyRecordsectionFactory = {
    _factoryFor: "StableTmSection";
    build: () => PromiseLike<Prisma.StableTmSectionCreateNestedOneWithoutTransportDailyRecordsInput["create"]>;
};
type StableTmTransportDailyRecordstableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutStableTmTransportDailyRecordInput["create"]>;
};
type StableTmTransportDailyRecordFactoryDefineInput = {
    transportDailyRecordId?: Buffer;
    transportDailyRecordInternalId?: (bigint | number);
    year?: number;
    month?: number;
    day?: number;
    isConfirmed?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    section: StableTmTransportDailyRecordsectionFactory | Prisma.StableTmSectionCreateNestedOneWithoutTransportDailyRecordsInput;
    stable: StableTmTransportDailyRecordstableFactory | Prisma.StableCreateNestedOneWithoutStableTmTransportDailyRecordInput;
    transportRecords?: Prisma.StableTmTransportRecordCreateNestedManyWithoutTransportDailyRecordInput;
};
type StableTmTransportDailyRecordTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmTransportDailyRecordFactoryDefineInput, never>>;
type StableTmTransportDailyRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmTransportDailyRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmTransportDailyRecord, Prisma.StableTmTransportDailyRecordCreateInput, TTransients>;
type StableTmTransportDailyRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmTransportDailyRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmTransportDailyRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmTransportDailyRecord, Prisma.StableTmTransportDailyRecordCreateInput, TTransients>;
type StableTmTransportDailyRecordTraitKeys<TOptions extends StableTmTransportDailyRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmTransportDailyRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmTransportDailyRecord";
    build(inputData?: Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportDailyRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportDailyRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmTransportDailyRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportDailyRecordCreateInput[]>;
    pickForConnect(inputData: StableTmTransportDailyRecord): Pick<StableTmTransportDailyRecord, "transportDailyRecordId">;
    create(inputData?: Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>): PromiseLike<StableTmTransportDailyRecord>;
    createList(list: readonly Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>[]): PromiseLike<StableTmTransportDailyRecord[]>;
    createList(count: number, item?: Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>): PromiseLike<StableTmTransportDailyRecord[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmTransportDailyRecordCreateInput & TTransients>): PromiseLike<Pick<StableTmTransportDailyRecord, "transportDailyRecordId">>;
}
export interface StableTmTransportDailyRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmTransportDailyRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmTransportDailyRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmTransportDailyRecordFactoryBuilder {
    <TOptions extends StableTmTransportDailyRecordFactoryDefineOptions>(options: TOptions): StableTmTransportDailyRecordFactoryInterface<{}, StableTmTransportDailyRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmTransportDailyRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmTransportDailyRecordFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmTransportDailyRecordFactoryInterface<TTransients, StableTmTransportDailyRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmTransportDailyRecord} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportDailyRecordFactoryInterface}
 */
export declare const defineStableTmTransportDailyRecordFactory: StableTmTransportDailyRecordFactoryBuilder;
type StableTmTransportRecordtransportDailyRecordFactory = {
    _factoryFor: "StableTmTransportDailyRecord";
    build: () => PromiseLike<Prisma.StableTmTransportDailyRecordCreateNestedOneWithoutTransportRecordsInput["create"]>;
};
type StableTmTransportRecordhorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutStableTmTransportRecordInput["create"]>;
};
type StableTmTransportRecordtransportInStatusFactory = {
    _factoryFor: "StableTmTransportInStatus";
    build: () => PromiseLike<Prisma.StableTmTransportInStatusCreateNestedOneWithoutTransportRecordInput["create"]>;
};
type StableTmTransportRecordtransportOutStatusFactory = {
    _factoryFor: "StableTmTransportOutStatus";
    build: () => PromiseLike<Prisma.StableTmTransportOutStatusCreateNestedOneWithoutTransportRecordInput["create"]>;
};
type StableTmTransportRecordFactoryDefineInput = {
    transportRecordId?: Buffer;
    transportRecordInternalId?: (bigint | number);
    type?: string;
    index?: string;
    createdAt?: Date;
    updatedAt?: Date;
    transportDailyRecord?: StableTmTransportRecordtransportDailyRecordFactory | Prisma.StableTmTransportDailyRecordCreateNestedOneWithoutTransportRecordsInput;
    horse: StableTmTransportRecordhorseFactory | Prisma.HorseCreateNestedOneWithoutStableTmTransportRecordInput;
    transportInStatus?: StableTmTransportRecordtransportInStatusFactory | Prisma.StableTmTransportInStatusCreateNestedOneWithoutTransportRecordInput;
    transportOutStatus?: StableTmTransportRecordtransportOutStatusFactory | Prisma.StableTmTransportOutStatusCreateNestedOneWithoutTransportRecordInput;
};
type StableTmTransportRecordTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmTransportRecordFactoryDefineInput, never>>;
type StableTmTransportRecordFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmTransportRecordFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmTransportRecord, Prisma.StableTmTransportRecordCreateInput, TTransients>;
type StableTmTransportRecordFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmTransportRecordFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmTransportRecordFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmTransportRecord, Prisma.StableTmTransportRecordCreateInput, TTransients>;
type StableTmTransportRecordTraitKeys<TOptions extends StableTmTransportRecordFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmTransportRecordFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmTransportRecord";
    build(inputData?: Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportRecordCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportRecordCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmTransportRecordCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportRecordCreateInput[]>;
    pickForConnect(inputData: StableTmTransportRecord): Pick<StableTmTransportRecord, "transportRecordId">;
    create(inputData?: Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>): PromiseLike<StableTmTransportRecord>;
    createList(list: readonly Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>[]): PromiseLike<StableTmTransportRecord[]>;
    createList(count: number, item?: Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>): PromiseLike<StableTmTransportRecord[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmTransportRecordCreateInput & TTransients>): PromiseLike<Pick<StableTmTransportRecord, "transportRecordId">>;
}
export interface StableTmTransportRecordFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmTransportRecordFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmTransportRecordFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmTransportRecordFactoryBuilder {
    <TOptions extends StableTmTransportRecordFactoryDefineOptions>(options: TOptions): StableTmTransportRecordFactoryInterface<{}, StableTmTransportRecordTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmTransportRecordTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmTransportRecordFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmTransportRecordFactoryInterface<TTransients, StableTmTransportRecordTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmTransportRecord} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportRecordFactoryInterface}
 */
export declare const defineStableTmTransportRecordFactory: StableTmTransportRecordFactoryBuilder;
type StableTmTransportInStatustransportRecordFactory = {
    _factoryFor: "StableTmTransportRecord";
    build: () => PromiseLike<Prisma.StableTmTransportRecordCreateNestedOneWithoutTransportInStatusInput["create"]>;
};
type StableTmTransportInStatusstaffFactory = {
    _factoryFor: "Staff";
    build: () => PromiseLike<Prisma.StaffCreateNestedOneWithoutStableTmTransportInStatusInput["create"]>;
};
type StableTmTransportInStatusFactoryDefineInput = {
    transportInStatusId?: Buffer;
    transportInStatusInternalId?: (bigint | number);
    isHorseVanArranged?: boolean;
    isQuarantineApplied?: boolean;
    isOwnerContacted?: boolean;
    isFarmContacted?: boolean;
    nextRace?: string | null;
    comment?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    transportRecord: StableTmTransportInStatustransportRecordFactory | Prisma.StableTmTransportRecordCreateNestedOneWithoutTransportInStatusInput;
    staff?: StableTmTransportInStatusstaffFactory | Prisma.StaffCreateNestedOneWithoutStableTmTransportInStatusInput;
};
type StableTmTransportInStatusTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmTransportInStatusFactoryDefineInput, never>>;
type StableTmTransportInStatusFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmTransportInStatusFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmTransportInStatus, Prisma.StableTmTransportInStatusCreateInput, TTransients>;
type StableTmTransportInStatusFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmTransportInStatusFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmTransportInStatusFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmTransportInStatus, Prisma.StableTmTransportInStatusCreateInput, TTransients>;
type StableTmTransportInStatusTraitKeys<TOptions extends StableTmTransportInStatusFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmTransportInStatusFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmTransportInStatus";
    build(inputData?: Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportInStatusCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportInStatusCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmTransportInStatusCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportInStatusCreateInput[]>;
    pickForConnect(inputData: StableTmTransportInStatus): Pick<StableTmTransportInStatus, "transportInStatusId">;
    create(inputData?: Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>): PromiseLike<StableTmTransportInStatus>;
    createList(list: readonly Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>[]): PromiseLike<StableTmTransportInStatus[]>;
    createList(count: number, item?: Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>): PromiseLike<StableTmTransportInStatus[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmTransportInStatusCreateInput & TTransients>): PromiseLike<Pick<StableTmTransportInStatus, "transportInStatusId">>;
}
export interface StableTmTransportInStatusFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmTransportInStatusFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmTransportInStatusFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmTransportInStatusFactoryBuilder {
    <TOptions extends StableTmTransportInStatusFactoryDefineOptions>(options: TOptions): StableTmTransportInStatusFactoryInterface<{}, StableTmTransportInStatusTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmTransportInStatusTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmTransportInStatusFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmTransportInStatusFactoryInterface<TTransients, StableTmTransportInStatusTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmTransportInStatus} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportInStatusFactoryInterface}
 */
export declare const defineStableTmTransportInStatusFactory: StableTmTransportInStatusFactoryBuilder;
type StableTmTransportOutStatustransportRecordFactory = {
    _factoryFor: "StableTmTransportRecord";
    build: () => PromiseLike<Prisma.StableTmTransportRecordCreateNestedOneWithoutTransportOutStatusInput["create"]>;
};
type StableTmTransportOutStatusstaffFactory = {
    _factoryFor: "Staff";
    build: () => PromiseLike<Prisma.StaffCreateNestedOneWithoutStableTmTransportOutStatusInput["create"]>;
};
type StableTmTransportOutStatusfarmFactory = {
    _factoryFor: "StableTmOutsideFarm";
    build: () => PromiseLike<Prisma.StableTmOutsideFarmCreateNestedOneWithoutStableTmTransportOutStatusInput["create"]>;
};
type StableTmTransportOutStatusFactoryDefineInput = {
    transportOutStatusId?: Buffer;
    transportOutStatusInternalId?: (bigint | number);
    isStableOutProcedureCompleted?: boolean;
    isHorseVanArranged?: boolean;
    isOwnerContacted?: boolean;
    isFarmContacted?: boolean;
    comment?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    transportRecord: StableTmTransportOutStatustransportRecordFactory | Prisma.StableTmTransportRecordCreateNestedOneWithoutTransportOutStatusInput;
    staff?: StableTmTransportOutStatusstaffFactory | Prisma.StaffCreateNestedOneWithoutStableTmTransportOutStatusInput;
    farm?: StableTmTransportOutStatusfarmFactory | Prisma.StableTmOutsideFarmCreateNestedOneWithoutStableTmTransportOutStatusInput;
    transportOutHandoverNotes?: Prisma.StableTmTransportOutHandoverNoteCreateNestedManyWithoutTransportOutStatusInput;
};
type StableTmTransportOutStatusTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmTransportOutStatusFactoryDefineInput, never>>;
type StableTmTransportOutStatusFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmTransportOutStatusFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmTransportOutStatus, Prisma.StableTmTransportOutStatusCreateInput, TTransients>;
type StableTmTransportOutStatusFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmTransportOutStatusFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmTransportOutStatusFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmTransportOutStatus, Prisma.StableTmTransportOutStatusCreateInput, TTransients>;
type StableTmTransportOutStatusTraitKeys<TOptions extends StableTmTransportOutStatusFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmTransportOutStatusFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmTransportOutStatus";
    build(inputData?: Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportOutStatusCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportOutStatusCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmTransportOutStatusCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportOutStatusCreateInput[]>;
    pickForConnect(inputData: StableTmTransportOutStatus): Pick<StableTmTransportOutStatus, "transportOutStatusId">;
    create(inputData?: Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>): PromiseLike<StableTmTransportOutStatus>;
    createList(list: readonly Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>[]): PromiseLike<StableTmTransportOutStatus[]>;
    createList(count: number, item?: Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>): PromiseLike<StableTmTransportOutStatus[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmTransportOutStatusCreateInput & TTransients>): PromiseLike<Pick<StableTmTransportOutStatus, "transportOutStatusId">>;
}
export interface StableTmTransportOutStatusFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmTransportOutStatusFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmTransportOutStatusFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmTransportOutStatusFactoryBuilder {
    <TOptions extends StableTmTransportOutStatusFactoryDefineOptions>(options: TOptions): StableTmTransportOutStatusFactoryInterface<{}, StableTmTransportOutStatusTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmTransportOutStatusTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmTransportOutStatusFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmTransportOutStatusFactoryInterface<TTransients, StableTmTransportOutStatusTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmTransportOutStatus} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportOutStatusFactoryInterface}
 */
export declare const defineStableTmTransportOutStatusFactory: StableTmTransportOutStatusFactoryBuilder;
type StableTmTransportOutHandoverNotetransportOutStatusFactory = {
    _factoryFor: "StableTmTransportOutStatus";
    build: () => PromiseLike<Prisma.StableTmTransportOutStatusCreateNestedOneWithoutTransportOutHandoverNotesInput["create"]>;
};
type StableTmTransportOutHandoverNoteFactoryDefineInput = {
    transportOutHandoverNoteId?: Buffer;
    transportOutHandoverNoteInternalId?: (bigint | number);
    body?: string | null;
    latestHorseShoeingDate?: Date | null;
    latestHorseShoeingFarrier?: string | null;
    latestHorseShoeingBody?: string | null;
    latestHorseBodyWeightDate?: Date | null;
    latestHorseBodyWeight?: string | null;
    createdAt?: Date;
    updatedAt?: Date;
    transportOutStatus: StableTmTransportOutHandoverNotetransportOutStatusFactory | Prisma.StableTmTransportOutStatusCreateNestedOneWithoutTransportOutHandoverNotesInput;
};
type StableTmTransportOutHandoverNoteTransientFields = Record<string, unknown> & Partial<Record<keyof StableTmTransportOutHandoverNoteFactoryDefineInput, never>>;
type StableTmTransportOutHandoverNoteFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<StableTmTransportOutHandoverNoteFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<StableTmTransportOutHandoverNote, Prisma.StableTmTransportOutHandoverNoteCreateInput, TTransients>;
type StableTmTransportOutHandoverNoteFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<StableTmTransportOutHandoverNoteFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: StableTmTransportOutHandoverNoteFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<StableTmTransportOutHandoverNote, Prisma.StableTmTransportOutHandoverNoteCreateInput, TTransients>;
type StableTmTransportOutHandoverNoteTraitKeys<TOptions extends StableTmTransportOutHandoverNoteFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface StableTmTransportOutHandoverNoteFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "StableTmTransportOutHandoverNote";
    build(inputData?: Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportOutHandoverNoteCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportOutHandoverNoteCreateInput>;
    buildList(list: readonly Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>[]): PromiseLike<Prisma.StableTmTransportOutHandoverNoteCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>): PromiseLike<Prisma.StableTmTransportOutHandoverNoteCreateInput[]>;
    pickForConnect(inputData: StableTmTransportOutHandoverNote): Pick<StableTmTransportOutHandoverNote, "transportOutHandoverNoteId">;
    create(inputData?: Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>): PromiseLike<StableTmTransportOutHandoverNote>;
    createList(list: readonly Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>[]): PromiseLike<StableTmTransportOutHandoverNote[]>;
    createList(count: number, item?: Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>): PromiseLike<StableTmTransportOutHandoverNote[]>;
    createForConnect(inputData?: Partial<Prisma.StableTmTransportOutHandoverNoteCreateInput & TTransients>): PromiseLike<Pick<StableTmTransportOutHandoverNote, "transportOutHandoverNoteId">>;
}
export interface StableTmTransportOutHandoverNoteFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends StableTmTransportOutHandoverNoteFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): StableTmTransportOutHandoverNoteFactoryInterfaceWithoutTraits<TTransients>;
}
interface StableTmTransportOutHandoverNoteFactoryBuilder {
    <TOptions extends StableTmTransportOutHandoverNoteFactoryDefineOptions>(options: TOptions): StableTmTransportOutHandoverNoteFactoryInterface<{}, StableTmTransportOutHandoverNoteTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends StableTmTransportOutHandoverNoteTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends StableTmTransportOutHandoverNoteFactoryDefineOptions<TTransients>>(options: TOptions) => StableTmTransportOutHandoverNoteFactoryInterface<TTransients, StableTmTransportOutHandoverNoteTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link StableTmTransportOutHandoverNote} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportOutHandoverNoteFactoryInterface}
 */
export declare const defineStableTmTransportOutHandoverNoteFactory: StableTmTransportOutHandoverNoteFactoryBuilder;
type OnetimeCodeFactoryDefineInput = {
    onetimeCodeInternalId?: (bigint | number);
    code?: string;
    sessionId?: string;
    expiresAt?: Date;
    verifiedAt?: Date | null;
    usedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};
type OnetimeCodeTransientFields = Record<string, unknown> & Partial<Record<keyof OnetimeCodeFactoryDefineInput, never>>;
type OnetimeCodeFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<OnetimeCodeFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<OnetimeCode, Prisma.OnetimeCodeCreateInput, TTransients>;
type OnetimeCodeFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<OnetimeCodeFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: OnetimeCodeFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<OnetimeCode, Prisma.OnetimeCodeCreateInput, TTransients>;
type OnetimeCodeTraitKeys<TOptions extends OnetimeCodeFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface OnetimeCodeFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "OnetimeCode";
    build(inputData?: Partial<Prisma.OnetimeCodeCreateInput & TTransients>): PromiseLike<Prisma.OnetimeCodeCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.OnetimeCodeCreateInput & TTransients>): PromiseLike<Prisma.OnetimeCodeCreateInput>;
    buildList(list: readonly Partial<Prisma.OnetimeCodeCreateInput & TTransients>[]): PromiseLike<Prisma.OnetimeCodeCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.OnetimeCodeCreateInput & TTransients>): PromiseLike<Prisma.OnetimeCodeCreateInput[]>;
    pickForConnect(inputData: OnetimeCode): Pick<OnetimeCode, "onetimeCodeInternalId">;
    create(inputData?: Partial<Prisma.OnetimeCodeCreateInput & TTransients>): PromiseLike<OnetimeCode>;
    createList(list: readonly Partial<Prisma.OnetimeCodeCreateInput & TTransients>[]): PromiseLike<OnetimeCode[]>;
    createList(count: number, item?: Partial<Prisma.OnetimeCodeCreateInput & TTransients>): PromiseLike<OnetimeCode[]>;
    createForConnect(inputData?: Partial<Prisma.OnetimeCodeCreateInput & TTransients>): PromiseLike<Pick<OnetimeCode, "onetimeCodeInternalId">>;
}
export interface OnetimeCodeFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends OnetimeCodeFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): OnetimeCodeFactoryInterfaceWithoutTraits<TTransients>;
}
interface OnetimeCodeFactoryBuilder {
    <TOptions extends OnetimeCodeFactoryDefineOptions>(options?: TOptions): OnetimeCodeFactoryInterface<{}, OnetimeCodeTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends OnetimeCodeTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends OnetimeCodeFactoryDefineOptions<TTransients>>(options?: TOptions) => OnetimeCodeFactoryInterface<TTransients, OnetimeCodeTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link OnetimeCode} model.
 *
 * @param options
 * @returns factory {@link OnetimeCodeFactoryInterface}
 */
export declare const defineOnetimeCodeFactory: OnetimeCodeFactoryBuilder;
type UserTermsAcceptancesownerFactory = {
    _factoryFor: "Owner";
    build: () => PromiseLike<Prisma.OwnerCreateNestedOneWithoutUserTermsAcceptancesInput["create"]>;
};
type UserTermsAcceptancesFactoryDefineInput = {
    userTermsAcceptanceId?: Buffer;
    acceptedAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    owner: UserTermsAcceptancesownerFactory | Prisma.OwnerCreateNestedOneWithoutUserTermsAcceptancesInput;
};
type UserTermsAcceptancesTransientFields = Record<string, unknown> & Partial<Record<keyof UserTermsAcceptancesFactoryDefineInput, never>>;
type UserTermsAcceptancesFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<UserTermsAcceptancesFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<UserTermsAcceptances, Prisma.UserTermsAcceptancesCreateInput, TTransients>;
type UserTermsAcceptancesFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<UserTermsAcceptancesFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: UserTermsAcceptancesFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<UserTermsAcceptances, Prisma.UserTermsAcceptancesCreateInput, TTransients>;
type UserTermsAcceptancesTraitKeys<TOptions extends UserTermsAcceptancesFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface UserTermsAcceptancesFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "UserTermsAcceptances";
    build(inputData?: Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>): PromiseLike<Prisma.UserTermsAcceptancesCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>): PromiseLike<Prisma.UserTermsAcceptancesCreateInput>;
    buildList(list: readonly Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>[]): PromiseLike<Prisma.UserTermsAcceptancesCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>): PromiseLike<Prisma.UserTermsAcceptancesCreateInput[]>;
    pickForConnect(inputData: UserTermsAcceptances): Pick<UserTermsAcceptances, "userTermsAcceptanceId">;
    create(inputData?: Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>): PromiseLike<UserTermsAcceptances>;
    createList(list: readonly Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>[]): PromiseLike<UserTermsAcceptances[]>;
    createList(count: number, item?: Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>): PromiseLike<UserTermsAcceptances[]>;
    createForConnect(inputData?: Partial<Prisma.UserTermsAcceptancesCreateInput & TTransients>): PromiseLike<Pick<UserTermsAcceptances, "userTermsAcceptanceId">>;
}
export interface UserTermsAcceptancesFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends UserTermsAcceptancesFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): UserTermsAcceptancesFactoryInterfaceWithoutTraits<TTransients>;
}
interface UserTermsAcceptancesFactoryBuilder {
    <TOptions extends UserTermsAcceptancesFactoryDefineOptions>(options: TOptions): UserTermsAcceptancesFactoryInterface<{}, UserTermsAcceptancesTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends UserTermsAcceptancesTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends UserTermsAcceptancesFactoryDefineOptions<TTransients>>(options: TOptions) => UserTermsAcceptancesFactoryInterface<TTransients, UserTermsAcceptancesTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link UserTermsAcceptances} model.
 *
 * @param options
 * @returns factory {@link UserTermsAcceptancesFactoryInterface}
 */
export declare const defineUserTermsAcceptancesFactory: UserTermsAcceptancesFactoryBuilder;
type HorseNoteUserDeviceuserFactory = {
    _factoryFor: "User";
    build: () => PromiseLike<Prisma.UserCreateNestedOneWithoutHorseNoteUserDeviceInput["create"]>;
};
type HorseNoteUserDeviceFactoryDefineInput = {
    deviceId?: string;
    osName?: string | null;
    osVersion?: string | null;
    modelName?: string | null;
    appRuntimeVersion?: string | null;
    appUpdateCreatedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    user?: HorseNoteUserDeviceuserFactory | Prisma.UserCreateNestedOneWithoutHorseNoteUserDeviceInput;
};
type HorseNoteUserDeviceTransientFields = Record<string, unknown> & Partial<Record<keyof HorseNoteUserDeviceFactoryDefineInput, never>>;
type HorseNoteUserDeviceFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseNoteUserDeviceFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseNoteUserDevice, Prisma.HorseNoteUserDeviceCreateInput, TTransients>;
type HorseNoteUserDeviceFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<HorseNoteUserDeviceFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: HorseNoteUserDeviceFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseNoteUserDevice, Prisma.HorseNoteUserDeviceCreateInput, TTransients>;
type HorseNoteUserDeviceTraitKeys<TOptions extends HorseNoteUserDeviceFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseNoteUserDeviceFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseNoteUserDevice";
    build(inputData?: Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>): PromiseLike<Prisma.HorseNoteUserDeviceCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>): PromiseLike<Prisma.HorseNoteUserDeviceCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>[]): PromiseLike<Prisma.HorseNoteUserDeviceCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>): PromiseLike<Prisma.HorseNoteUserDeviceCreateInput[]>;
    pickForConnect(inputData: HorseNoteUserDevice): Pick<HorseNoteUserDevice, "deviceId">;
    create(inputData?: Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>): PromiseLike<HorseNoteUserDevice>;
    createList(list: readonly Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>[]): PromiseLike<HorseNoteUserDevice[]>;
    createList(count: number, item?: Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>): PromiseLike<HorseNoteUserDevice[]>;
    createForConnect(inputData?: Partial<Prisma.HorseNoteUserDeviceCreateInput & TTransients>): PromiseLike<Pick<HorseNoteUserDevice, "deviceId">>;
}
export interface HorseNoteUserDeviceFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseNoteUserDeviceFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseNoteUserDeviceFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseNoteUserDeviceFactoryBuilder {
    <TOptions extends HorseNoteUserDeviceFactoryDefineOptions>(options?: TOptions): HorseNoteUserDeviceFactoryInterface<{}, HorseNoteUserDeviceTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseNoteUserDeviceTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseNoteUserDeviceFactoryDefineOptions<TTransients>>(options?: TOptions) => HorseNoteUserDeviceFactoryInterface<TTransients, HorseNoteUserDeviceTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseNoteUserDevice} model.
 *
 * @param options
 * @returns factory {@link HorseNoteUserDeviceFactoryInterface}
 */
export declare const defineHorseNoteUserDeviceFactory: HorseNoteUserDeviceFactoryBuilder;
type FeatureFlagorganizationFactory = {
    _factoryFor: "Organization";
    build: () => PromiseLike<Prisma.OrganizationCreateNestedOneWithoutFeatureFlagInput["create"]>;
};
type FeatureFlagFactoryDefineInput = {
    featureFlagId?: (bigint | number);
    monthlyReport?: boolean;
    businessTrip?: boolean;
    languageSetting?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    organization: FeatureFlagorganizationFactory | Prisma.OrganizationCreateNestedOneWithoutFeatureFlagInput;
};
type FeatureFlagTransientFields = Record<string, unknown> & Partial<Record<keyof FeatureFlagFactoryDefineInput, never>>;
type FeatureFlagFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<FeatureFlagFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<FeatureFlag, Prisma.FeatureFlagCreateInput, TTransients>;
type FeatureFlagFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<FeatureFlagFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: FeatureFlagFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<FeatureFlag, Prisma.FeatureFlagCreateInput, TTransients>;
type FeatureFlagTraitKeys<TOptions extends FeatureFlagFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface FeatureFlagFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "FeatureFlag";
    build(inputData?: Partial<Prisma.FeatureFlagCreateInput & TTransients>): PromiseLike<Prisma.FeatureFlagCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.FeatureFlagCreateInput & TTransients>): PromiseLike<Prisma.FeatureFlagCreateInput>;
    buildList(list: readonly Partial<Prisma.FeatureFlagCreateInput & TTransients>[]): PromiseLike<Prisma.FeatureFlagCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.FeatureFlagCreateInput & TTransients>): PromiseLike<Prisma.FeatureFlagCreateInput[]>;
    pickForConnect(inputData: FeatureFlag): Pick<FeatureFlag, "featureFlagId">;
    create(inputData?: Partial<Prisma.FeatureFlagCreateInput & TTransients>): PromiseLike<FeatureFlag>;
    createList(list: readonly Partial<Prisma.FeatureFlagCreateInput & TTransients>[]): PromiseLike<FeatureFlag[]>;
    createList(count: number, item?: Partial<Prisma.FeatureFlagCreateInput & TTransients>): PromiseLike<FeatureFlag[]>;
    createForConnect(inputData?: Partial<Prisma.FeatureFlagCreateInput & TTransients>): PromiseLike<Pick<FeatureFlag, "featureFlagId">>;
}
export interface FeatureFlagFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends FeatureFlagFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): FeatureFlagFactoryInterfaceWithoutTraits<TTransients>;
}
interface FeatureFlagFactoryBuilder {
    <TOptions extends FeatureFlagFactoryDefineOptions>(options: TOptions): FeatureFlagFactoryInterface<{}, FeatureFlagTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends FeatureFlagTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends FeatureFlagFactoryDefineOptions<TTransients>>(options: TOptions) => FeatureFlagFactoryInterface<TTransients, FeatureFlagTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link FeatureFlag} model.
 *
 * @param options
 * @returns factory {@link FeatureFlagFactoryInterface}
 */
export declare const defineFeatureFlagFactory: FeatureFlagFactoryBuilder;
type BatchJobFactoryDefineInput = {
    id?: string;
    name?: string;
    description?: string | null;
    status?: BatchStatus;
    startedAt?: Date | null;
    completedAt?: Date | null;
    errorMessage?: string | null;
    metadata?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue;
    createdAt?: Date;
    updatedAt?: Date;
    BatchLog?: Prisma.BatchLogCreateNestedManyWithoutJobInput;
};
type BatchJobTransientFields = Record<string, unknown> & Partial<Record<keyof BatchJobFactoryDefineInput, never>>;
type BatchJobFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<BatchJobFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<BatchJob, Prisma.BatchJobCreateInput, TTransients>;
type BatchJobFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<BatchJobFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: BatchJobFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<BatchJob, Prisma.BatchJobCreateInput, TTransients>;
type BatchJobTraitKeys<TOptions extends BatchJobFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface BatchJobFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "BatchJob";
    build(inputData?: Partial<Prisma.BatchJobCreateInput & TTransients>): PromiseLike<Prisma.BatchJobCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.BatchJobCreateInput & TTransients>): PromiseLike<Prisma.BatchJobCreateInput>;
    buildList(list: readonly Partial<Prisma.BatchJobCreateInput & TTransients>[]): PromiseLike<Prisma.BatchJobCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.BatchJobCreateInput & TTransients>): PromiseLike<Prisma.BatchJobCreateInput[]>;
    pickForConnect(inputData: BatchJob): Pick<BatchJob, "id">;
    create(inputData?: Partial<Prisma.BatchJobCreateInput & TTransients>): PromiseLike<BatchJob>;
    createList(list: readonly Partial<Prisma.BatchJobCreateInput & TTransients>[]): PromiseLike<BatchJob[]>;
    createList(count: number, item?: Partial<Prisma.BatchJobCreateInput & TTransients>): PromiseLike<BatchJob[]>;
    createForConnect(inputData?: Partial<Prisma.BatchJobCreateInput & TTransients>): PromiseLike<Pick<BatchJob, "id">>;
}
export interface BatchJobFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends BatchJobFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): BatchJobFactoryInterfaceWithoutTraits<TTransients>;
}
interface BatchJobFactoryBuilder {
    <TOptions extends BatchJobFactoryDefineOptions>(options?: TOptions): BatchJobFactoryInterface<{}, BatchJobTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends BatchJobTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends BatchJobFactoryDefineOptions<TTransients>>(options?: TOptions) => BatchJobFactoryInterface<TTransients, BatchJobTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link BatchJob} model.
 *
 * @param options
 * @returns factory {@link BatchJobFactoryInterface}
 */
export declare const defineBatchJobFactory: BatchJobFactoryBuilder;
type BatchLogjobFactory = {
    _factoryFor: "BatchJob";
    build: () => PromiseLike<Prisma.BatchJobCreateNestedOneWithoutBatchLogInput["create"]>;
};
type BatchLogFactoryDefineInput = {
    id?: string;
    level?: LogLevel;
    message?: string;
    metadata?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue;
    createdAt?: Date;
    job?: BatchLogjobFactory | Prisma.BatchJobCreateNestedOneWithoutBatchLogInput;
};
type BatchLogTransientFields = Record<string, unknown> & Partial<Record<keyof BatchLogFactoryDefineInput, never>>;
type BatchLogFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<BatchLogFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<BatchLog, Prisma.BatchLogCreateInput, TTransients>;
type BatchLogFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<BatchLogFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: BatchLogFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<BatchLog, Prisma.BatchLogCreateInput, TTransients>;
type BatchLogTraitKeys<TOptions extends BatchLogFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface BatchLogFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "BatchLog";
    build(inputData?: Partial<Prisma.BatchLogCreateInput & TTransients>): PromiseLike<Prisma.BatchLogCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.BatchLogCreateInput & TTransients>): PromiseLike<Prisma.BatchLogCreateInput>;
    buildList(list: readonly Partial<Prisma.BatchLogCreateInput & TTransients>[]): PromiseLike<Prisma.BatchLogCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.BatchLogCreateInput & TTransients>): PromiseLike<Prisma.BatchLogCreateInput[]>;
    pickForConnect(inputData: BatchLog): Pick<BatchLog, "id">;
    create(inputData?: Partial<Prisma.BatchLogCreateInput & TTransients>): PromiseLike<BatchLog>;
    createList(list: readonly Partial<Prisma.BatchLogCreateInput & TTransients>[]): PromiseLike<BatchLog[]>;
    createList(count: number, item?: Partial<Prisma.BatchLogCreateInput & TTransients>): PromiseLike<BatchLog[]>;
    createForConnect(inputData?: Partial<Prisma.BatchLogCreateInput & TTransients>): PromiseLike<Pick<BatchLog, "id">>;
}
export interface BatchLogFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends BatchLogFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): BatchLogFactoryInterfaceWithoutTraits<TTransients>;
}
interface BatchLogFactoryBuilder {
    <TOptions extends BatchLogFactoryDefineOptions>(options?: TOptions): BatchLogFactoryInterface<{}, BatchLogTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends BatchLogTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends BatchLogFactoryDefineOptions<TTransients>>(options?: TOptions) => BatchLogFactoryInterface<TTransients, BatchLogTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link BatchLog} model.
 *
 * @param options
 * @returns factory {@link BatchLogFactoryInterface}
 */
export declare const defineBatchLogFactory: BatchLogFactoryBuilder;
type TraininghorseFactory = {
    _factoryFor: "Horse";
    build: () => PromiseLike<Prisma.HorseCreateNestedOneWithoutTrainingsInput["create"]>;
};
type TrainingstableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutTrainingsInput["create"]>;
};
type TrainingFactoryDefineInput = {
    trainingId?: (bigint | number);
    trainerUuid?: Buffer | null;
    trainerId?: (bigint | number) | null;
    startAt?: Date | null;
    endAt?: Date | null;
    analysisStartedAt?: Date | null;
    analysisEndedAt?: Date | null;
    locationDataPath?: string | null;
    locationDataExt?: string | null;
    gyroAndAccDataPath?: string | null;
    gyroAndAccDataExt?: string | null;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    timeSeriesHeartBeatResults?: Prisma.TimeSeriesHeartBeatResultCreateNestedManyWithoutTrainingInput;
    trainingIndicators?: Prisma.TrainingIndicatorCreateNestedManyWithoutTrainingInput;
    trainingPeriods?: Prisma.TrainingPeriodCreateNestedManyWithoutTrainingInput;
    gaitAnalysisResult?: Prisma.GaitAnalysisResultCreateNestedManyWithoutTrainingInput;
    horse?: TraininghorseFactory | Prisma.HorseCreateNestedOneWithoutTrainingsInput;
    stable?: TrainingstableFactory | Prisma.StableCreateNestedOneWithoutTrainingsInput;
};
type TrainingTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingFactoryDefineInput, never>>;
type TrainingFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<Training, Prisma.TrainingCreateInput, TTransients>;
type TrainingFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<TrainingFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: TrainingFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<Training, Prisma.TrainingCreateInput, TTransients>;
type TrainingTraitKeys<TOptions extends TrainingFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "Training";
    build(inputData?: Partial<Prisma.TrainingCreateInput & TTransients>): PromiseLike<Prisma.TrainingCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingCreateInput & TTransients>): PromiseLike<Prisma.TrainingCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingCreateInput & TTransients>): PromiseLike<Prisma.TrainingCreateInput[]>;
    pickForConnect(inputData: Training): Pick<Training, "trainingId">;
    create(inputData?: Partial<Prisma.TrainingCreateInput & TTransients>): PromiseLike<Training>;
    createList(list: readonly Partial<Prisma.TrainingCreateInput & TTransients>[]): PromiseLike<Training[]>;
    createList(count: number, item?: Partial<Prisma.TrainingCreateInput & TTransients>): PromiseLike<Training[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingCreateInput & TTransients>): PromiseLike<Pick<Training, "trainingId">>;
}
export interface TrainingFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingFactoryBuilder {
    <TOptions extends TrainingFactoryDefineOptions>(options?: TOptions): TrainingFactoryInterface<{}, TrainingTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingFactoryDefineOptions<TTransients>>(options?: TOptions) => TrainingFactoryInterface<TTransients, TrainingTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link Training} model.
 *
 * @param options
 * @returns factory {@link TrainingFactoryInterface}
 */
export declare const defineTrainingFactory: TrainingFactoryBuilder;
type TrainingIndicatortrainingFactory = {
    _factoryFor: "Training";
    build: () => PromiseLike<Prisma.TrainingCreateNestedOneWithoutTrainingIndicatorsInput["create"]>;
};
type TrainingIndicatorFactoryDefineInput = {
    trainingIndicatorId?: (bigint | number);
    periodGroupId?: number;
    facilityId?: (bigint | number) | null;
    courseId?: string | null;
    maxHeartRate?: number | null;
    maxHeartRateInAll?: number | null;
    maxHeartRateInLap?: number | null;
    thr100?: number | null;
    v200?: number | null;
    oneMinuteHeartRate?: number | null;
    threeMinutesMinHeartRate?: number | null;
    heartRateGap?: number | null;
    thirtySecondsAfterGoalHeartRate?: number | null;
    oneMinuteAfterGoalHeartRate?: number | null;
    twoMinutesAfterGoalHeartRate?: number | null;
    twoMinutesAfterGoalMinHeartRate?: number | null;
    threeMinutesAfterGoalMinHeartRate?: number | null;
    createdAt?: Date;
    updatedAt?: Date;
    training: TrainingIndicatortrainingFactory | Prisma.TrainingCreateNestedOneWithoutTrainingIndicatorsInput;
    TrainingIndicatorLabel?: Prisma.TrainingIndicatorLabelCreateNestedManyWithoutTrainingIndicatorInput;
};
type TrainingIndicatorTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingIndicatorFactoryDefineInput, never>>;
type TrainingIndicatorFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingIndicatorFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingIndicator, Prisma.TrainingIndicatorCreateInput, TTransients>;
type TrainingIndicatorFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<TrainingIndicatorFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: TrainingIndicatorFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingIndicator, Prisma.TrainingIndicatorCreateInput, TTransients>;
type TrainingIndicatorTraitKeys<TOptions extends TrainingIndicatorFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingIndicatorFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingIndicator";
    build(inputData?: Partial<Prisma.TrainingIndicatorCreateInput & TTransients>): PromiseLike<Prisma.TrainingIndicatorCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingIndicatorCreateInput & TTransients>): PromiseLike<Prisma.TrainingIndicatorCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingIndicatorCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingIndicatorCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingIndicatorCreateInput & TTransients>): PromiseLike<Prisma.TrainingIndicatorCreateInput[]>;
    pickForConnect(inputData: TrainingIndicator): Pick<TrainingIndicator, "trainingIndicatorId">;
    create(inputData?: Partial<Prisma.TrainingIndicatorCreateInput & TTransients>): PromiseLike<TrainingIndicator>;
    createList(list: readonly Partial<Prisma.TrainingIndicatorCreateInput & TTransients>[]): PromiseLike<TrainingIndicator[]>;
    createList(count: number, item?: Partial<Prisma.TrainingIndicatorCreateInput & TTransients>): PromiseLike<TrainingIndicator[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingIndicatorCreateInput & TTransients>): PromiseLike<Pick<TrainingIndicator, "trainingIndicatorId">>;
}
export interface TrainingIndicatorFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingIndicatorFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingIndicatorFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingIndicatorFactoryBuilder {
    <TOptions extends TrainingIndicatorFactoryDefineOptions>(options: TOptions): TrainingIndicatorFactoryInterface<{}, TrainingIndicatorTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingIndicatorTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingIndicatorFactoryDefineOptions<TTransients>>(options: TOptions) => TrainingIndicatorFactoryInterface<TTransients, TrainingIndicatorTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingIndicator} model.
 *
 * @param options
 * @returns factory {@link TrainingIndicatorFactoryInterface}
 */
export declare const defineTrainingIndicatorFactory: TrainingIndicatorFactoryBuilder;
type TrainingPeriodtrainingFactory = {
    _factoryFor: "Training";
    build: () => PromiseLike<Prisma.TrainingCreateNestedOneWithoutTrainingPeriodsInput["create"]>;
};
type TrainingPeriodtrainingCourseFactory = {
    _factoryFor: "TrainingCourse";
    build: () => PromiseLike<Prisma.TrainingCourseCreateNestedOneWithoutTrainingPeriodInput["create"]>;
};
type TrainingPeriodFactoryDefineInput = {
    trainingPeriodUuid?: Buffer;
    periodGroupId?: number;
    periodType?: PeriodType;
    facilityId?: (bigint | number) | null;
    direction?: string | null;
    startAt?: Date | null;
    startTime?: number;
    endTime?: number;
    startDistance?: number;
    endDistance?: number;
    lapCount?: number | null;
    isMain?: boolean;
    leftLegRatio?: number | null;
    totalDistance?: number | null;
    createdAt?: Date;
    updatedAt?: Date;
    training: TrainingPeriodtrainingFactory | Prisma.TrainingCreateNestedOneWithoutTrainingPeriodsInput;
    trainingCourse?: TrainingPeriodtrainingCourseFactory | Prisma.TrainingCourseCreateNestedOneWithoutTrainingPeriodInput;
};
type TrainingPeriodTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingPeriodFactoryDefineInput, never>>;
type TrainingPeriodFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingPeriodFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingPeriod, Prisma.TrainingPeriodCreateInput, TTransients>;
type TrainingPeriodFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<TrainingPeriodFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: TrainingPeriodFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingPeriod, Prisma.TrainingPeriodCreateInput, TTransients>;
type TrainingPeriodTraitKeys<TOptions extends TrainingPeriodFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingPeriodFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingPeriod";
    build(inputData?: Partial<Prisma.TrainingPeriodCreateInput & TTransients>): PromiseLike<Prisma.TrainingPeriodCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingPeriodCreateInput & TTransients>): PromiseLike<Prisma.TrainingPeriodCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingPeriodCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingPeriodCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingPeriodCreateInput & TTransients>): PromiseLike<Prisma.TrainingPeriodCreateInput[]>;
    pickForConnect(inputData: TrainingPeriod): Pick<TrainingPeriod, "trainingPeriodUuid">;
    create(inputData?: Partial<Prisma.TrainingPeriodCreateInput & TTransients>): PromiseLike<TrainingPeriod>;
    createList(list: readonly Partial<Prisma.TrainingPeriodCreateInput & TTransients>[]): PromiseLike<TrainingPeriod[]>;
    createList(count: number, item?: Partial<Prisma.TrainingPeriodCreateInput & TTransients>): PromiseLike<TrainingPeriod[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingPeriodCreateInput & TTransients>): PromiseLike<Pick<TrainingPeriod, "trainingPeriodUuid">>;
}
export interface TrainingPeriodFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingPeriodFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingPeriodFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingPeriodFactoryBuilder {
    <TOptions extends TrainingPeriodFactoryDefineOptions>(options: TOptions): TrainingPeriodFactoryInterface<{}, TrainingPeriodTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingPeriodTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingPeriodFactoryDefineOptions<TTransients>>(options: TOptions) => TrainingPeriodFactoryInterface<TTransients, TrainingPeriodTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingPeriod} model.
 *
 * @param options
 * @returns factory {@link TrainingPeriodFactoryInterface}
 */
export declare const defineTrainingPeriodFactory: TrainingPeriodFactoryBuilder;
type TrainingIndicatorLabeltrainingIndicatorFactory = {
    _factoryFor: "TrainingIndicator";
    build: () => PromiseLike<Prisma.TrainingIndicatorCreateNestedOneWithoutTrainingIndicatorLabelInput["create"]>;
};
type TrainingIndicatorLabelFactoryDefineInput = {
    trainingIndicatorLabelId?: (bigint | number);
    label?: string;
    time?: number;
    distance?: number;
    createdAt?: Date;
    updatedAt?: Date;
    trainingIndicator: TrainingIndicatorLabeltrainingIndicatorFactory | Prisma.TrainingIndicatorCreateNestedOneWithoutTrainingIndicatorLabelInput;
};
type TrainingIndicatorLabelTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingIndicatorLabelFactoryDefineInput, never>>;
type TrainingIndicatorLabelFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingIndicatorLabelFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingIndicatorLabel, Prisma.TrainingIndicatorLabelCreateInput, TTransients>;
type TrainingIndicatorLabelFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<TrainingIndicatorLabelFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: TrainingIndicatorLabelFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingIndicatorLabel, Prisma.TrainingIndicatorLabelCreateInput, TTransients>;
type TrainingIndicatorLabelTraitKeys<TOptions extends TrainingIndicatorLabelFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingIndicatorLabelFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingIndicatorLabel";
    build(inputData?: Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>): PromiseLike<Prisma.TrainingIndicatorLabelCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>): PromiseLike<Prisma.TrainingIndicatorLabelCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingIndicatorLabelCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>): PromiseLike<Prisma.TrainingIndicatorLabelCreateInput[]>;
    pickForConnect(inputData: TrainingIndicatorLabel): Pick<TrainingIndicatorLabel, "trainingIndicatorLabelId">;
    create(inputData?: Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>): PromiseLike<TrainingIndicatorLabel>;
    createList(list: readonly Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>[]): PromiseLike<TrainingIndicatorLabel[]>;
    createList(count: number, item?: Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>): PromiseLike<TrainingIndicatorLabel[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingIndicatorLabelCreateInput & TTransients>): PromiseLike<Pick<TrainingIndicatorLabel, "trainingIndicatorLabelId">>;
}
export interface TrainingIndicatorLabelFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingIndicatorLabelFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingIndicatorLabelFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingIndicatorLabelFactoryBuilder {
    <TOptions extends TrainingIndicatorLabelFactoryDefineOptions>(options: TOptions): TrainingIndicatorLabelFactoryInterface<{}, TrainingIndicatorLabelTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingIndicatorLabelTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingIndicatorLabelFactoryDefineOptions<TTransients>>(options: TOptions) => TrainingIndicatorLabelFactoryInterface<TTransients, TrainingIndicatorLabelTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingIndicatorLabel} model.
 *
 * @param options
 * @returns factory {@link TrainingIndicatorLabelFactoryInterface}
 */
export declare const defineTrainingIndicatorLabelFactory: TrainingIndicatorLabelFactoryBuilder;
type TimeSeriesHeartBeatResulttrainingFactory = {
    _factoryFor: "Training";
    build: () => PromiseLike<Prisma.TrainingCreateNestedOneWithoutTimeSeriesHeartBeatResultsInput["create"]>;
};
type TimeSeriesHeartBeatResultFactoryDefineInput = {
    timeSeriesHeartBeatResultId?: (bigint | number);
    time?: number;
    heartRate?: number | null;
    sympatheticNerve?: number | null;
    parasympatheticNerve?: number | null;
    createdAt?: Date;
    training: TimeSeriesHeartBeatResulttrainingFactory | Prisma.TrainingCreateNestedOneWithoutTimeSeriesHeartBeatResultsInput;
};
type TimeSeriesHeartBeatResultTransientFields = Record<string, unknown> & Partial<Record<keyof TimeSeriesHeartBeatResultFactoryDefineInput, never>>;
type TimeSeriesHeartBeatResultFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TimeSeriesHeartBeatResultFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TimeSeriesHeartBeatResult, Prisma.TimeSeriesHeartBeatResultCreateInput, TTransients>;
type TimeSeriesHeartBeatResultFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<TimeSeriesHeartBeatResultFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: TimeSeriesHeartBeatResultFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TimeSeriesHeartBeatResult, Prisma.TimeSeriesHeartBeatResultCreateInput, TTransients>;
type TimeSeriesHeartBeatResultTraitKeys<TOptions extends TimeSeriesHeartBeatResultFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TimeSeriesHeartBeatResultFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TimeSeriesHeartBeatResult";
    build(inputData?: Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>): PromiseLike<Prisma.TimeSeriesHeartBeatResultCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>): PromiseLike<Prisma.TimeSeriesHeartBeatResultCreateInput>;
    buildList(list: readonly Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>[]): PromiseLike<Prisma.TimeSeriesHeartBeatResultCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>): PromiseLike<Prisma.TimeSeriesHeartBeatResultCreateInput[]>;
    pickForConnect(inputData: TimeSeriesHeartBeatResult): Pick<TimeSeriesHeartBeatResult, "timeSeriesHeartBeatResultId">;
    create(inputData?: Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>): PromiseLike<TimeSeriesHeartBeatResult>;
    createList(list: readonly Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>[]): PromiseLike<TimeSeriesHeartBeatResult[]>;
    createList(count: number, item?: Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>): PromiseLike<TimeSeriesHeartBeatResult[]>;
    createForConnect(inputData?: Partial<Prisma.TimeSeriesHeartBeatResultCreateInput & TTransients>): PromiseLike<Pick<TimeSeriesHeartBeatResult, "timeSeriesHeartBeatResultId">>;
}
export interface TimeSeriesHeartBeatResultFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TimeSeriesHeartBeatResultFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TimeSeriesHeartBeatResultFactoryInterfaceWithoutTraits<TTransients>;
}
interface TimeSeriesHeartBeatResultFactoryBuilder {
    <TOptions extends TimeSeriesHeartBeatResultFactoryDefineOptions>(options: TOptions): TimeSeriesHeartBeatResultFactoryInterface<{}, TimeSeriesHeartBeatResultTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TimeSeriesHeartBeatResultTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TimeSeriesHeartBeatResultFactoryDefineOptions<TTransients>>(options: TOptions) => TimeSeriesHeartBeatResultFactoryInterface<TTransients, TimeSeriesHeartBeatResultTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TimeSeriesHeartBeatResult} model.
 *
 * @param options
 * @returns factory {@link TimeSeriesHeartBeatResultFactoryInterface}
 */
export declare const defineTimeSeriesHeartBeatResultFactory: TimeSeriesHeartBeatResultFactoryBuilder;
type GaitAnalysisResulttrainingFactory = {
    _factoryFor: "Training";
    build: () => PromiseLike<Prisma.TrainingCreateNestedOneWithoutGaitAnalysisResultInput["create"]>;
};
type GaitAnalysisResultFactoryDefineInput = {
    gaitAnalysisResultUuid?: Buffer;
    startAt?: Date;
    endAt?: Date;
    gait?: Gait | null;
    impactLeftFront?: number | null;
    impactRightFront?: number | null;
    impactLeftBack?: number | null;
    impactRightBack?: number | null;
    swingTimeRatioLeftFront?: number | null;
    swingTimeRatioRightFront?: number | null;
    swingTimeRatioLeftBack?: number | null;
    swingTimeRatioRightBack?: number | null;
    footOnAngleLeftFront?: number | null;
    footOnAngleRightFront?: number | null;
    footOnAngleLeftBack?: number | null;
    footOnAngleRightBack?: number | null;
    footOffAngleLeftFront?: number | null;
    footOffAngleRightFront?: number | null;
    footOffAngleLeftBack?: number | null;
    footOffAngleRightBack?: number | null;
    createdAt?: Date;
    training: GaitAnalysisResulttrainingFactory | Prisma.TrainingCreateNestedOneWithoutGaitAnalysisResultInput;
};
type GaitAnalysisResultTransientFields = Record<string, unknown> & Partial<Record<keyof GaitAnalysisResultFactoryDefineInput, never>>;
type GaitAnalysisResultFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<GaitAnalysisResultFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<GaitAnalysisResult, Prisma.GaitAnalysisResultCreateInput, TTransients>;
type GaitAnalysisResultFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<GaitAnalysisResultFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: GaitAnalysisResultFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<GaitAnalysisResult, Prisma.GaitAnalysisResultCreateInput, TTransients>;
type GaitAnalysisResultTraitKeys<TOptions extends GaitAnalysisResultFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface GaitAnalysisResultFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "GaitAnalysisResult";
    build(inputData?: Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>): PromiseLike<Prisma.GaitAnalysisResultCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>): PromiseLike<Prisma.GaitAnalysisResultCreateInput>;
    buildList(list: readonly Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>[]): PromiseLike<Prisma.GaitAnalysisResultCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>): PromiseLike<Prisma.GaitAnalysisResultCreateInput[]>;
    pickForConnect(inputData: GaitAnalysisResult): Pick<GaitAnalysisResult, "gaitAnalysisResultUuid">;
    create(inputData?: Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>): PromiseLike<GaitAnalysisResult>;
    createList(list: readonly Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>[]): PromiseLike<GaitAnalysisResult[]>;
    createList(count: number, item?: Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>): PromiseLike<GaitAnalysisResult[]>;
    createForConnect(inputData?: Partial<Prisma.GaitAnalysisResultCreateInput & TTransients>): PromiseLike<Pick<GaitAnalysisResult, "gaitAnalysisResultUuid">>;
}
export interface GaitAnalysisResultFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends GaitAnalysisResultFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): GaitAnalysisResultFactoryInterfaceWithoutTraits<TTransients>;
}
interface GaitAnalysisResultFactoryBuilder {
    <TOptions extends GaitAnalysisResultFactoryDefineOptions>(options: TOptions): GaitAnalysisResultFactoryInterface<{}, GaitAnalysisResultTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends GaitAnalysisResultTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends GaitAnalysisResultFactoryDefineOptions<TTransients>>(options: TOptions) => GaitAnalysisResultFactoryInterface<TTransients, GaitAnalysisResultTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link GaitAnalysisResult} model.
 *
 * @param options
 * @returns factory {@link GaitAnalysisResultFactoryInterface}
 */
export declare const defineGaitAnalysisResultFactory: GaitAnalysisResultFactoryBuilder;
type HorseCoursePitchAveragemasterHorseFactory = {
    _factoryFor: "MasterHorse";
    build: () => PromiseLike<Prisma.MasterHorseCreateNestedOneWithoutHorseCoursePitchAverageInput["create"]>;
};
type HorseCoursePitchAveragecourseFactory = {
    _factoryFor: "TrainingCourse";
    build: () => PromiseLike<Prisma.TrainingCourseCreateNestedOneWithoutHorseCoursePitchAverageInput["create"]>;
};
type HorseCoursePitchAverageFactoryDefineInput = {
    horseCoursePitchAverageUuid?: Buffer;
    speed?: number;
    relativeAveragePitch?: number | null;
    absoluteAveragePitch?: number | null;
    deviationScore?: number | null;
    createdAt?: Date;
    updatedAt?: Date;
    masterHorse: HorseCoursePitchAveragemasterHorseFactory | Prisma.MasterHorseCreateNestedOneWithoutHorseCoursePitchAverageInput;
    course: HorseCoursePitchAveragecourseFactory | Prisma.TrainingCourseCreateNestedOneWithoutHorseCoursePitchAverageInput;
};
type HorseCoursePitchAverageTransientFields = Record<string, unknown> & Partial<Record<keyof HorseCoursePitchAverageFactoryDefineInput, never>>;
type HorseCoursePitchAverageFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<HorseCoursePitchAverageFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<HorseCoursePitchAverage, Prisma.HorseCoursePitchAverageCreateInput, TTransients>;
type HorseCoursePitchAverageFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<HorseCoursePitchAverageFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: HorseCoursePitchAverageFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<HorseCoursePitchAverage, Prisma.HorseCoursePitchAverageCreateInput, TTransients>;
type HorseCoursePitchAverageTraitKeys<TOptions extends HorseCoursePitchAverageFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface HorseCoursePitchAverageFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "HorseCoursePitchAverage";
    build(inputData?: Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>): PromiseLike<Prisma.HorseCoursePitchAverageCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>): PromiseLike<Prisma.HorseCoursePitchAverageCreateInput>;
    buildList(list: readonly Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>[]): PromiseLike<Prisma.HorseCoursePitchAverageCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>): PromiseLike<Prisma.HorseCoursePitchAverageCreateInput[]>;
    pickForConnect(inputData: HorseCoursePitchAverage): Pick<HorseCoursePitchAverage, "horseCoursePitchAverageUuid">;
    create(inputData?: Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>): PromiseLike<HorseCoursePitchAverage>;
    createList(list: readonly Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>[]): PromiseLike<HorseCoursePitchAverage[]>;
    createList(count: number, item?: Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>): PromiseLike<HorseCoursePitchAverage[]>;
    createForConnect(inputData?: Partial<Prisma.HorseCoursePitchAverageCreateInput & TTransients>): PromiseLike<Pick<HorseCoursePitchAverage, "horseCoursePitchAverageUuid">>;
}
export interface HorseCoursePitchAverageFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends HorseCoursePitchAverageFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): HorseCoursePitchAverageFactoryInterfaceWithoutTraits<TTransients>;
}
interface HorseCoursePitchAverageFactoryBuilder {
    <TOptions extends HorseCoursePitchAverageFactoryDefineOptions>(options: TOptions): HorseCoursePitchAverageFactoryInterface<{}, HorseCoursePitchAverageTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends HorseCoursePitchAverageTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends HorseCoursePitchAverageFactoryDefineOptions<TTransients>>(options: TOptions) => HorseCoursePitchAverageFactoryInterface<TTransients, HorseCoursePitchAverageTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link HorseCoursePitchAverage} model.
 *
 * @param options
 * @returns factory {@link HorseCoursePitchAverageFactoryInterface}
 */
export declare const defineHorseCoursePitchAverageFactory: HorseCoursePitchAverageFactoryBuilder;
type TrainingMenustableFactory = {
    _factoryFor: "Stable";
    build: () => PromiseLike<Prisma.StableCreateNestedOneWithoutTrainingMenusInput["create"]>;
};
type TrainingMenuFactoryDefineInput = {
    trainingMenuUuid?: Buffer;
    trainingMenuInternalId?: (bigint | number);
    trainingMenuName?: string;
    trainingType?: string | null;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    stable: TrainingMenustableFactory | Prisma.StableCreateNestedOneWithoutTrainingMenusInput;
    horseTrainingRecords?: Prisma.HorseTrainingRecordCreateNestedManyWithoutTrainingMenuInput;
};
type TrainingMenuTransientFields = Record<string, unknown> & Partial<Record<keyof TrainingMenuFactoryDefineInput, never>>;
type TrainingMenuFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainingMenuFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainingMenu, Prisma.TrainingMenuCreateInput, TTransients>;
type TrainingMenuFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData: Resolver<TrainingMenuFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: string | symbol]: TrainingMenuFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainingMenu, Prisma.TrainingMenuCreateInput, TTransients>;
type TrainingMenuTraitKeys<TOptions extends TrainingMenuFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainingMenuFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainingMenu";
    build(inputData?: Partial<Prisma.TrainingMenuCreateInput & TTransients>): PromiseLike<Prisma.TrainingMenuCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainingMenuCreateInput & TTransients>): PromiseLike<Prisma.TrainingMenuCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainingMenuCreateInput & TTransients>[]): PromiseLike<Prisma.TrainingMenuCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainingMenuCreateInput & TTransients>): PromiseLike<Prisma.TrainingMenuCreateInput[]>;
    pickForConnect(inputData: TrainingMenu): Pick<TrainingMenu, "trainingMenuUuid">;
    create(inputData?: Partial<Prisma.TrainingMenuCreateInput & TTransients>): PromiseLike<TrainingMenu>;
    createList(list: readonly Partial<Prisma.TrainingMenuCreateInput & TTransients>[]): PromiseLike<TrainingMenu[]>;
    createList(count: number, item?: Partial<Prisma.TrainingMenuCreateInput & TTransients>): PromiseLike<TrainingMenu[]>;
    createForConnect(inputData?: Partial<Prisma.TrainingMenuCreateInput & TTransients>): PromiseLike<Pick<TrainingMenu, "trainingMenuUuid">>;
}
export interface TrainingMenuFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainingMenuFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainingMenuFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainingMenuFactoryBuilder {
    <TOptions extends TrainingMenuFactoryDefineOptions>(options: TOptions): TrainingMenuFactoryInterface<{}, TrainingMenuTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainingMenuTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainingMenuFactoryDefineOptions<TTransients>>(options: TOptions) => TrainingMenuFactoryInterface<TTransients, TrainingMenuTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainingMenu} model.
 *
 * @param options
 * @returns factory {@link TrainingMenuFactoryInterface}
 */
export declare const defineTrainingMenuFactory: TrainingMenuFactoryBuilder;
type TrainersUserSettingsuserFactory = {
    _factoryFor: "User";
    build: () => PromiseLike<Prisma.UserCreateNestedOneWithoutTrainersUserSettingsInput["create"]>;
};
type TrainersUserSettingsFactoryDefineInput = {
    trainersUserSettingsId?: Buffer;
    reportUnreadFilterTruncateEnabled?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    user?: TrainersUserSettingsuserFactory | Prisma.UserCreateNestedOneWithoutTrainersUserSettingsInput;
};
type TrainersUserSettingsTransientFields = Record<string, unknown> & Partial<Record<keyof TrainersUserSettingsFactoryDefineInput, never>>;
type TrainersUserSettingsFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<TrainersUserSettingsFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<TrainersUserSettings, Prisma.TrainersUserSettingsCreateInput, TTransients>;
type TrainersUserSettingsFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<TrainersUserSettingsFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: TrainersUserSettingsFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<TrainersUserSettings, Prisma.TrainersUserSettingsCreateInput, TTransients>;
type TrainersUserSettingsTraitKeys<TOptions extends TrainersUserSettingsFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface TrainersUserSettingsFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "TrainersUserSettings";
    build(inputData?: Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>): PromiseLike<Prisma.TrainersUserSettingsCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>): PromiseLike<Prisma.TrainersUserSettingsCreateInput>;
    buildList(list: readonly Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>[]): PromiseLike<Prisma.TrainersUserSettingsCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>): PromiseLike<Prisma.TrainersUserSettingsCreateInput[]>;
    pickForConnect(inputData: TrainersUserSettings): Pick<TrainersUserSettings, "trainersUserSettingsId">;
    create(inputData?: Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>): PromiseLike<TrainersUserSettings>;
    createList(list: readonly Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>[]): PromiseLike<TrainersUserSettings[]>;
    createList(count: number, item?: Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>): PromiseLike<TrainersUserSettings[]>;
    createForConnect(inputData?: Partial<Prisma.TrainersUserSettingsCreateInput & TTransients>): PromiseLike<Pick<TrainersUserSettings, "trainersUserSettingsId">>;
}
export interface TrainersUserSettingsFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends TrainersUserSettingsFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): TrainersUserSettingsFactoryInterfaceWithoutTraits<TTransients>;
}
interface TrainersUserSettingsFactoryBuilder {
    <TOptions extends TrainersUserSettingsFactoryDefineOptions>(options?: TOptions): TrainersUserSettingsFactoryInterface<{}, TrainersUserSettingsTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends TrainersUserSettingsTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends TrainersUserSettingsFactoryDefineOptions<TTransients>>(options?: TOptions) => TrainersUserSettingsFactoryInterface<TTransients, TrainersUserSettingsTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link TrainersUserSettings} model.
 *
 * @param options
 * @returns factory {@link TrainersUserSettingsFactoryInterface}
 */
export declare const defineTrainersUserSettingsFactory: TrainersUserSettingsFactoryBuilder;
type UserLangSettinguserFactory = {
    _factoryFor: "User";
    build: () => PromiseLike<Prisma.UserCreateNestedOneWithoutUserLangSettingsInput["create"]>;
};
type UserLangSettingFactoryDefineInput = {
    userLangSettingId?: (bigint | number);
    lang?: string;
    createdAt?: Date;
    updatedAt?: Date;
    user?: UserLangSettinguserFactory | Prisma.UserCreateNestedOneWithoutUserLangSettingsInput;
};
type UserLangSettingTransientFields = Record<string, unknown> & Partial<Record<keyof UserLangSettingFactoryDefineInput, never>>;
type UserLangSettingFactoryTrait<TTransients extends Record<string, unknown>> = {
    data?: Resolver<Partial<UserLangSettingFactoryDefineInput>, BuildDataOptions<TTransients>>;
} & CallbackDefineOptions<UserLangSetting, Prisma.UserLangSettingCreateInput, TTransients>;
type UserLangSettingFactoryDefineOptions<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
    defaultData?: Resolver<UserLangSettingFactoryDefineInput, BuildDataOptions<TTransients>>;
    traits?: {
        [traitName: TraitName]: UserLangSettingFactoryTrait<TTransients>;
    };
} & CallbackDefineOptions<UserLangSetting, Prisma.UserLangSettingCreateInput, TTransients>;
type UserLangSettingTraitKeys<TOptions extends UserLangSettingFactoryDefineOptions<any>> = Exclude<keyof TOptions["traits"], number>;
export interface UserLangSettingFactoryInterfaceWithoutTraits<TTransients extends Record<string, unknown>> {
    readonly _factoryFor: "UserLangSetting";
    build(inputData?: Partial<Prisma.UserLangSettingCreateInput & TTransients>): PromiseLike<Prisma.UserLangSettingCreateInput>;
    buildCreateInput(inputData?: Partial<Prisma.UserLangSettingCreateInput & TTransients>): PromiseLike<Prisma.UserLangSettingCreateInput>;
    buildList(list: readonly Partial<Prisma.UserLangSettingCreateInput & TTransients>[]): PromiseLike<Prisma.UserLangSettingCreateInput[]>;
    buildList(count: number, item?: Partial<Prisma.UserLangSettingCreateInput & TTransients>): PromiseLike<Prisma.UserLangSettingCreateInput[]>;
    pickForConnect(inputData: UserLangSetting): Pick<UserLangSetting, "userLangSettingId">;
    create(inputData?: Partial<Prisma.UserLangSettingCreateInput & TTransients>): PromiseLike<UserLangSetting>;
    createList(list: readonly Partial<Prisma.UserLangSettingCreateInput & TTransients>[]): PromiseLike<UserLangSetting[]>;
    createList(count: number, item?: Partial<Prisma.UserLangSettingCreateInput & TTransients>): PromiseLike<UserLangSetting[]>;
    createForConnect(inputData?: Partial<Prisma.UserLangSettingCreateInput & TTransients>): PromiseLike<Pick<UserLangSetting, "userLangSettingId">>;
}
export interface UserLangSettingFactoryInterface<TTransients extends Record<string, unknown> = Record<string, unknown>, TTraitName extends TraitName = TraitName> extends UserLangSettingFactoryInterfaceWithoutTraits<TTransients> {
    use(name: TTraitName, ...names: readonly TTraitName[]): UserLangSettingFactoryInterfaceWithoutTraits<TTransients>;
}
interface UserLangSettingFactoryBuilder {
    <TOptions extends UserLangSettingFactoryDefineOptions>(options?: TOptions): UserLangSettingFactoryInterface<{}, UserLangSettingTraitKeys<TOptions>>;
    withTransientFields: <TTransients extends UserLangSettingTransientFields>(defaultTransientFieldValues: TTransients) => <TOptions extends UserLangSettingFactoryDefineOptions<TTransients>>(options?: TOptions) => UserLangSettingFactoryInterface<TTransients, UserLangSettingTraitKeys<TOptions>>;
}
/**
 * Define factory for {@link UserLangSetting} model.
 *
 * @param options
 * @returns factory {@link UserLangSettingFactoryInterface}
 */
export declare const defineUserLangSettingFactory: UserLangSettingFactoryBuilder;
