model OnetimeCode {
  onetimeCodeInternalId BigInt    @id @default(autoincrement()) @map("onetime_code_internal_id")
  code                  String    @map("code") @db.VarChar(50)
  sessionId             String    @unique @map("session_id") @db.Var<PERSON>har(50)
  expiresAt             DateTime  @map("expires_at")
  verifiedAt            DateTime? @map("verified_at")
  usedAt                DateTime? @map("used_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  @@map("onetime_code")
}

model UserTermsAcceptances {
  userTermsAcceptanceId Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("user_terms_acceptance_id") @db.Binary(16)
  acceptedAt            DateTime @map("accepted_at")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  ownerId               String   @map("owner_id")

  owner Owner @relation(fields: [ownerId], references: [ownerId])

  @@map("user_terms_acceptances")
}

model HorseNoteUserDevice {
  deviceId           String    @id @map("device_id")
  osName             String?   @map("os_name")
  osVersion          String?   @map("os_version")
  modelName          String?   @map("model_name")
  appRuntimeVersion  String?   @map("app_runtime_version")
  appUpdateCreatedAt DateTime? @map("app_update_created_at")
  userUuid           Bytes?    @map("user_uuid") @db.Binary(16)
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")

  user User? @relation(fields: [userUuid], references: [userUuid])

  @@map("horse_note_user_devices")
}

model FeatureFlag {
  featureFlagId    BigInt   @id @default(autoincrement()) @map("feature_flag_id")
  organizationUuid Bytes    @map("organization_uuid") @db.Binary(16)
  monthlyReport    Boolean  @default(false) @map("monthly_report")
  businessTrip     Boolean  @default(false) @map("business_trip")
  languageSetting  Boolean  @default(false) @map("language_setting")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@unique([organizationUuid])
  @@map("feature_flags")
}

model BatchJob {
    id           String      @id @default(cuid())
    name         String
    description  String?
    status       BatchStatus @default(PENDING)
    startedAt    DateTime?
    completedAt  DateTime?
    errorMessage String?
    metadata     Json?
    createdAt    DateTime    @default(now())
    updatedAt    DateTime    @updatedAt

    BatchLog     BatchLog[]

    @@map("batch_jobs")
}

model BatchLog {
    id        String   @id @default(cuid())
    jobId     String?
    level     LogLevel @default(INFO)
    message   String
    metadata  Json?
    createdAt DateTime @default(now())

    job BatchJob? @relation(fields: [jobId], references: [id])

    @@map("batch_logs")
}

enum BatchStatus {
    PENDING
    RUNNING
    COMPLETED
    FAILED
    CANCELLED
}

enum LogLevel {
    DEBUG
    INFO
    WARN
    ERROR
}
