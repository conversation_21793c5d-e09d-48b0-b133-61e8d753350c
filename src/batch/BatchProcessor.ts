import { PrismaClient } from '@prisma/client';
import { testRepository } from '@/repositories/test_repository';
import { logger } from '@/utils/logger';

export class BatchProcessor {
  private prisma: PrismaClient;
  private readonly jobs: Record<string, () => Promise<void>>;

  constructor() {
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });

    // Register available jobs
    this.jobs = {
      'example-job': this.exampleJob.bind(this),
      'data-cleanup': this.dataCleanupJob.bind(this),
      'test-repository': this.testRepositoryJob.bind(this),
      // Add more jobs here
    };
  }

  async initialize(): Promise<void> {
    try {
      await this.prisma.$connect();
      logger.info('Database connection established');
    } catch (error) {
      logger.error('Failed to connect to database', { error });
      throw error;
    }
  }

  async executeJob(jobName: string): Promise<void> {
    const job = this.jobs[jobName];
    if (!job) {
      throw new Error(
        `Unknown job: ${jobName}. Available jobs: ${Object.keys(this.jobs).join(', ')}`,
      );
    }

    const startTime = Date.now();
    let batchJobRecord: { id: string } | undefined;

    try {
      // Create batch job record
      batchJobRecord = await this.prisma.batchJob.create({
        data: {
          name: jobName,
          description: `Batch job: ${jobName}`,
          status: 'RUNNING',
          startedAt: new Date(),
        },
      });

      logger.info(`Starting job: ${jobName}`, { jobId: batchJobRecord.id });

      // Execute the job
      await job();

      // Update job status to completed
      await this.prisma.batchJob.update({
        where: { id: batchJobRecord.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
        },
      });

      const duration = Date.now() - startTime;
      logger.info(`Job completed successfully: ${jobName}`, {
        jobId: batchJobRecord.id,
        duration: `${duration}ms`,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Job failed: ${jobName}`, {
        jobId: batchJobRecord?.id,
        duration: `${duration}ms`,
        error,
      });

      // Update job status to failed
      if (batchJobRecord) {
        await this.prisma.batchJob.update({
          where: { id: batchJobRecord.id },
          data: {
            status: 'FAILED',
            completedAt: new Date(),
            errorMessage: error instanceof Error ? error.message : String(error),
          },
        });
      }

      throw error;
    }
  }

  private async exampleJob(): Promise<void> {
    logger.info('Executing example job...');

    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Log some information
    await this.prisma.batchLog.create({
      data: {
        level: 'INFO',
        message: 'Example job executed successfully',
        metadata: {
          timestamp: new Date().toISOString(),
          jobType: 'example',
        },
      },
    });

    logger.info('Example job completed');
  }

  private async dataCleanupJob(): Promise<void> {
    logger.info('Executing data cleanup job...');

    // Example: Clean up old batch logs (older than 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await this.prisma.batchLog.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo,
        },
      },
    });

    logger.info('Data cleanup completed', { deletedLogs: result.count });
  }

  private async testRepositoryJob(): Promise<void> {
    logger.info('Executing repository test job...');
    await testRepository();
    logger.info('Repository test job completed');
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
    logger.info('Database connection closed');
  }
}
