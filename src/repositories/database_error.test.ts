import { describe, expect, it } from 'vitest';
import { DatabaseError } from './database_error';

describe('DatabaseError', () => {
  it('Errorクラスを継承している', () => {
    const error = new DatabaseError('Test error message');
    expect(error).toBeInstanceOf(Error);
  });

  it('nameプロパティが正しく設定されている', () => {
    const error = new DatabaseError('Test error message');
    expect(error.name).toBe('DatabaseError');
  });

  it('メッセージが正しく設定されている', () => {
    const message = 'Database connection failed';
    const error = new DatabaseError(message);
    expect(error.message).toBe(message);
  });

  it('nameプロパティがreadonlyである（TypeScriptレベル）', () => {
    const error = new DatabaseError('Test error message');

    // TypeScriptのコンパイル時チェックのため、実際の実行時テストは難しいが
    // nameプロパティの型がreadonlyであることを確認
    expect(error.name).toBe('DatabaseError');

    // JavaScriptの実行時にはreadonlyは強制されないため、
    // TypeScriptのコンパイル時チェックに依存する
    // 実際のテストでは値が正しく設定されていることのみ確認
    expect(typeof error.name).toBe('string');
  });
});
