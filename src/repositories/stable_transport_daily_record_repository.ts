import { PrismaClient, type StableTmTransportDailyRecord } from '@prisma/client';
import type { Result } from 'neverthrow';
import { err, ok, ResultAsync } from 'neverthrow';
import { client as prisma } from './client';
import { DatabaseError } from './database_error';
import type { RepositoryFunction as RF } from './repository';

export const getUnconfirmedRecordsBeforeDate: RF<
  { date: Date },
  StableTmTransportDailyRecord[]
> = async ({ date }, tx) => {
  const client = tx || prisma;
  const targetYear = date.getFullYear();
  const targetMonth = date.getMonth() + 1; // JavaScript months are 0-indexed
  const targetDay = date.getDate();

  const result = ResultAsync.fromPromise(
    client.stableTmTransportDailyRecord.findMany({
      where: {
        isConfirmed: false,
        OR: [
          {
            year: {
              lt: targetYear,
            },
          },
          {
            year: targetYear,
            month: {
              lt: targetMonth,
            },
          },
          {
            year: targetYear,
            month: targetMonth,
            day: {
              lt: targetDay,
            },
          },
        ],
      },
      orderBy: [{ year: 'asc' }, { month: 'asc' }, { day: 'asc' }],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      const e = err(new DatabaseError(`Failed to fetch unconfirmed records: ${message}`));
      console.error(e);
      return e;
    },
  );
  return result;
};
